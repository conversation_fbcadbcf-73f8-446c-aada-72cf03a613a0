import backtrader as bt
import pandas as pd
import yfinance as yf

tsla_df = yf.download(tickers='TSLA')

tsla_df

tsla_df_parsed = bt.feeds.PandasData(dataname=tsla_df, datetime=None, open=0, high=1, low=2, close=4, volume=5, openinterest=-1)

tsla_df.to_csv('tsla.csv')

df = pd.read_csv('tsla.csv')

df

tsla_csv_parsed = bt.feeds.GenericCSVData(dataname='tsla.csv', datetime=0, open=1, high=2, low=3, close=5, volume=6, openinterest=-1, dtformat='%Y-%m-%d')

cerebro = bt.Cerebro()
cerebro.adddata(tsla_csv_parsed)

cerebro.run()
%matplotlib inline
cerebro.plot(iplot=False)

