{"cells": [{"cell_type": "code", "execution_count": 1, "id": "52cea146-1948-4d6e-8dc8-c3e639829d93", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "plt.style.use('seaborn-v0_8-notebook')\n", "import yfinance as yf\n", "import backtrader as bt\n", "import datetime"]}, {"cell_type": "markdown", "id": "cecd3207-b197-411c-b552-a2762a5d4667", "metadata": {}, "source": ["### Momentum\n", "\n", "if close[2] > close [1] ---> buy if close [2] < close [1] sell"]}, {"cell_type": "code", "execution_count": 2, "id": "b4a9a285-72f0-48ca-98ae-023ea609b696", "metadata": {}, "outputs": [], "source": ["class momentum(bt.Strategy):\n", "    def __init__(self):\n", "        self.dataclose = self.datas[0].close\n", "    def next(self):\n", "        if self.dataclose[-2] > self.dataclose[-1]:\n", "            self.order = self.buy()\n", "            print(f'buy executed @ {self.dataclose[0]}')\n", "        if self.dataclose[-2] < self.dataclose[-1]:\n", "            self.order = self.sell()\n", "            print(f'sell executed @ {self.dataclose[0]}')\n", "        else:\n", "            self.order = self.close()"]}, {"cell_type": "code", "execution_count": 3, "id": "ce7b8446-753a-4e0c-9f49-8600158a13f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.addstrategy(momentum)"]}, {"cell_type": "code", "execution_count": 4, "id": "9be81bcc-fc40-49f6-aadf-e9008ad2efaa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n"]}, {"data": {"text/plain": ["<backtrader.feeds.pandafeed.PandasData at 0x204d3517a10>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["tsla_daily = yf.download(tickers='TSLA')\n", "tsla_bt = bt.feeds.PandasData(dataname = tsla_daily)\n", "cerebro.adddata(tsla_bt)"]}, {"cell_type": "code", "execution_count": 5, "id": "b18d527a-6889-474b-a310-29b62c8eda67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sell executed @ 1.5926669836044312\n", "buy executed @ 1.5886670351028442\n", "buy executed @ 1.4639999866485596\n", "buy executed @ 1.2799999713897705\n", "buy executed @ 1.0740000009536743\n", "buy executed @ 1.053333044052124\n", "buy executed @ 1.1640000343322754\n", "sell executed @ 1.159999966621399\n", "buy executed @ 1.136667013168335\n", "buy executed @ 1.2093329429626465\n", "sell executed @ 1.3226670026779175\n", "sell executed @ 1.3259999752044678\n", "sell executed @ 1.3760000467300415\n", "sell executed @ 1.4606670141220093\n", "sell executed @ 1.3533329963684082\n", "buy executed @ 1.3480000495910645\n", "buy executed @ 1.399999976158142\n", "sell executed @ 1.4193329811096191\n", "sell executed @ 1.3966670036315918\n", "buy executed @ 1.3700000047683716\n", "buy executed @ 1.3813329935073853\n", "sell executed @ 1.3566670417785645\n", "buy executed @ 1.329332947731018\n", "buy executed @ 1.3946670293807983\n", "sell executed @ 1.463333010673523\n", "sell executed @ 1.4173330068588257\n", "buy executed @ 1.363332986831665\n", "buy executed @ 1.305999994277954\n", "buy executed @ 1.3066669702529907\n", "sell executed @ 1.2686669826507568\n", "buy executed @ 1.1933330297470093\n", "buy executed @ 1.1733330488204956\n", "buy executed @ 1.2213330268859863\n", "sell executed @ 1.2519999742507935\n", "sell executed @ 1.2766669988632202\n", "sell executed @ 1.2513329982757568\n", "buy executed @ 1.25266695022583\n", "sell executed @ 1.273332953453064\n", "sell executed @ 1.3420000076293945\n", "sell executed @ 1.2799999713897705\n", "buy executed @ 1.3266669511795044\n", "sell executed @ 1.3166669607162476\n", "buy executed @ 1.3133330345153809\n", "buy executed @ 1.324666976928711\n", "sell executed @ 1.2986669540405273\n", "buy executed @ 1.363332986831665\n", "sell executed @ 1.4040000438690186\n", "sell executed @ 1.4033329486846924\n", "buy executed @ 1.369333028793335\n", "buy executed @ 1.3933329582214355\n", "sell executed @ 1.380666971206665\n", "buy executed @ 1.3446669578552246\n", "buy executed @ 1.3813329935073853\n", "sell executed @ 1.4079999923706055\n", "sell executed @ 1.4653329849243164\n", "sell executed @ 1.3960000276565552\n", "buy executed @ 1.348667025566101\n", "buy executed @ 1.4040000438690186\n", "sell executed @ 1.3846670389175415\n", "buy executed @ 1.324666976928711\n", "buy executed @ 1.3040000200271606\n", "buy executed @ 1.340000033378601\n", "sell executed @ 1.3686670064926147\n", "sell executed @ 1.4266669750213623\n", "sell executed @ 1.4653329849243164\n", "sell executed @ 1.3606669902801514\n", "buy executed @ 1.3733329772949219\n", "sell executed @ 1.3993330001831055\n", "sell executed @ 1.4079999923706055\n", "sell executed @ 1.3639999628067017\n", "buy executed @ 1.3619999885559082\n", "buy executed @ 1.3619999885559082\n", "buy executed @ 1.3493330478668213\n", "sell executed @ 1.3833329677581787\n", "sell executed @ 1.369333028793335\n", "buy executed @ 1.348667025566101\n", "buy executed @ 1.3366669416427612\n", "buy executed @ 1.3766670227050781\n", "sell executed @ 1.3833329677581787\n", "sell executed @ 1.3813329935073853\n", "buy executed @ 1.3899999856948853\n", "sell executed @ 1.4240000247955322\n", "sell executed @ 1.399999976158142\n", "buy executed @ 1.4126670360565186\n", "sell executed @ 1.4559999704360962\n", "sell executed @ 1.4273329973220825\n", "buy executed @ 1.4166669845581055\n", "buy executed @ 1.4513330459594727\n", "sell executed @ 1.659999966621399\n", "sell executed @ 1.6293330192565918\n", "buy executed @ 1.6653330326080322\n", "sell executed @ 1.6419999599456787\n", "buy executed @ 1.957332968711853\n", "sell executed @ 1.869333028793335\n", "buy executed @ 1.9893330335617065\n", "sell executed @ 2.053333044052124\n", "sell executed @ 1.9780000448226929\n", "buy executed @ 1.965999960899353\n", "buy executed @ 1.9926669597625732\n", "sell executed @ 2.065999984741211\n", "sell executed @ 2.2266669273376465\n", "sell executed @ 2.3046669960021973\n", "sell executed @ 2.3646669387817383\n", "sell executed @ 2.3546669483184814\n", "buy executed @ 2.2886669635772705\n", "buy executed @ 2.355333089828491\n", "sell executed @ 2.2899999618530273\n", "buy executed @ 2.1566669940948486\n", "buy executed @ 2.0993330478668213\n", "buy executed @ 2.02066707611084\n", "buy executed @ 2.1040000915527344\n", "sell executed @ 2.1579999923706055\n", "sell executed @ 2.136667013168335\n", "buy executed @ 2.101332902908325\n", "buy executed @ 2.0366671085357666\n", "buy executed @ 1.9019999504089355\n", "buy executed @ 1.9733330011367798\n", "sell executed @ 2.053999900817871\n", "sell executed @ 2.0906670093536377\n", "sell executed @ 2.113332986831665\n", "sell executed @ 2.1506669521331787\n", "sell executed @ 2.175333023071289\n", "sell executed @ 2.00600004196167\n", "buy executed @ 1.7033330202102661\n", "buy executed @ 1.7606669664382935\n", "sell executed @ 1.848667025566101\n", "sell executed @ 1.7666670083999634\n", "buy executed @ 1.775333046913147\n", "sell executed @ 1.7746670246124268\n", "buy executed @ 1.777999997138977\n", "sell executed @ 1.7886669635772705\n", "sell executed @ 1.858667016029358\n", "sell executed @ 1.8826669454574585\n", "sell executed @ 1.8966670036315918\n", "sell executed @ 1.797333002090454\n", "buy executed @ 1.797333002090454\n", "buy executed @ 1.7166670560836792\n", "buy executed @ 1.7093329429626465\n", "buy executed @ 1.6019999980926514\n", "buy executed @ 1.5080000162124634\n", "buy executed @ 1.5360000133514404\n", "sell executed @ 1.6326669454574585\n", "sell executed @ 1.6453330516815186\n", "sell executed @ 1.649999976158142\n", "sell executed @ 1.6613329648971558\n", "sell executed @ 1.6006669998168945\n", "buy executed @ 1.6066670417785645\n", "sell executed @ 1.593999981880188\n", "buy executed @ 1.5959999561309814\n", "sell executed @ 1.5753329992294312\n", "buy executed @ 1.5640000104904175\n", "buy executed @ 1.5379999876022339\n", "buy executed @ 1.6326669454574585\n", "sell executed @ 1.547333002090454\n", "buy executed @ 1.5479999780654907\n", "sell executed @ 1.5499999523162842\n", "sell executed @ 1.5386669635772705\n", "buy executed @ 1.5226670503616333\n", "buy executed @ 1.6486669778823853\n", "sell executed @ 1.5733330249786377\n", "buy executed @ 1.5453330278396606\n", "buy executed @ 1.4579999446868896\n", "buy executed @ 1.4553329944610596\n", "buy executed @ 1.5019999742507935\n", "sell executed @ 1.5740000009536743\n", "sell executed @ 1.5926669836044312\n", "sell executed @ 1.5959999561309814\n", "sell executed @ 1.6013330221176147\n", "sell executed @ 1.6239999532699585\n", "sell executed @ 1.6633330583572388\n", "sell executed @ 1.6626670360565186\n", "buy executed @ 1.6440000534057617\n", "buy executed @ 1.6480000019073486\n", "sell executed @ 1.6006669998168945\n", "buy executed @ 1.6046669483184814\n", "sell executed @ 1.5499999523162842\n", "buy executed @ 1.5299999713897705\n", "buy executed @ 1.5213329792022705\n", "buy executed @ 1.5206669569015503\n", "buy executed @ 1.5306669473648071\n", "sell executed @ 1.5153330564498901\n", "buy executed @ 1.4793330430984497\n", "buy executed @ 1.480666995048523\n", "sell executed @ 1.4886670112609863\n", "sell executed @ 1.5166670083999634\n", "sell executed @ 1.5499999523162842\n", "sell executed @ 1.5946669578552246\n", "sell executed @ 1.5806670188903809\n", "buy executed @ 1.850000023841858\n", "sell executed @ 1.7773330211639404\n", "buy executed @ 1.722000002861023\n", "buy executed @ 1.7799999713897705\n", "sell executed @ 1.7660000324249268\n", "buy executed @ 1.815999984741211\n", "sell executed @ 1.7660000324249268\n", "buy executed @ 1.6846669912338257\n", "buy executed @ 1.6433329582214355\n", "buy executed @ 1.6619999408721924\n", "sell executed @ 1.6759999990463257\n", "sell executed @ 1.7053329944610596\n", "sell executed @ 1.668666958808899\n", "buy executed @ 1.6773329973220825\n", "sell executed @ 1.7166670560836792\n", "sell executed @ 1.7826670408248901\n", "sell executed @ 1.7593330144882202\n", "buy executed @ 1.7953330278396606\n", "sell executed @ 1.8053330183029175\n", "sell executed @ 1.843999981880188\n", "sell executed @ 1.840000033378601\n", "buy executed @ 1.8300000429153442\n", "buy executed @ 1.7913329601287842\n", "buy executed @ 1.7793329954147339\n", "buy executed @ 1.762666940689087\n", "buy executed @ 1.8079999685287476\n", "sell executed @ 1.8606669902801514\n", "sell executed @ 1.8886669874191284\n", "sell executed @ 1.8046669960021973\n", "buy executed @ 1.8446669578552246\n", "sell executed @ 1.8366669416427612\n", "buy executed @ 1.773332953453064\n", "buy executed @ 1.730666995048523\n", "buy executed @ 1.7566670179367065\n", "sell executed @ 1.8799999952316284\n", "sell executed @ 1.8646670579910278\n", "buy executed @ 1.7879999876022339\n", "buy executed @ 1.7813329696655273\n", "buy executed @ 1.9320000410079956\n", "sell executed @ 1.9653329849243164\n", "sell executed @ 1.9700000286102295\n", "sell executed @ 2.0093328952789307\n", "sell executed @ 1.901332974433899\n", "buy executed @ 1.9173330068588257\n", "sell executed @ 2.0086669921875\n", "sell executed @ 1.9133330583572388\n", "buy executed @ 1.891332983970642\n", "buy executed @ 1.8079999685287476\n", "buy executed @ 1.841333031654358\n", "sell executed @ 1.8573329448699951\n", "sell executed @ 1.8953330516815186\n", "sell executed @ 1.9066669940948486\n", "sell executed @ 1.8213330507278442\n", "buy executed @ 1.7666670083999634\n", "buy executed @ 1.7666670083999634\n", "buy executed @ 1.835332989692688\n", "sell executed @ 1.8140000104904175\n", "buy executed @ 1.8473329544067383\n", "sell executed @ 1.8380000591278076\n", "buy executed @ 1.8306670188903809\n", "buy executed @ 1.8739999532699585\n", "sell executed @ 1.8860000371932983\n", "sell executed @ 1.9420000314712524\n", "sell executed @ 1.9346669912338257\n", "buy executed @ 1.942667007446289\n", "sell executed @ 1.9306670427322388\n", "buy executed @ 1.9819999933242798\n", "sell executed @ 1.920667052268982\n", "buy executed @ 1.8899999856948853\n", "buy executed @ 1.878000020980835\n", "buy executed @ 1.9093329906463623\n", "sell executed @ 1.8406670093536377\n", "buy executed @ 1.8386670351028442\n", "buy executed @ 1.8153330087661743\n", "buy executed @ 1.8593330383300781\n", "sell executed @ 1.9126670360565186\n", "sell executed @ 1.9133330583572388\n", "sell executed @ 1.952666997909546\n", "sell executed @ 1.8993330001831055\n", "buy executed @ 1.8666670322418213\n", "buy executed @ 1.8426669836044312\n", "buy executed @ 1.878000020980835\n", "sell executed @ 1.878000020980835\n", "sell executed @ 1.8226670026779175\n", "buy executed @ 1.8133330345153809\n", "buy executed @ 1.649999976158142\n", "buy executed @ 1.6160000562667847\n", "buy executed @ 1.5759999752044678\n", "buy executed @ 1.670667052268982\n", "sell executed @ 1.5880000591278076\n", "buy executed @ 1.6866669654846191\n", "sell executed @ 1.753999948501587\n", "sell executed @ 1.7486670017242432\n", "buy executed @ 1.7400000095367432\n", "buy executed @ 1.722000002861023\n", "buy executed @ 1.6173330545425415\n", "buy executed @ 1.4866670370101929\n", "buy executed @ 1.463333010673523\n", "buy executed @ 1.5306669473648071\n", "sell executed @ 1.591333031654358\n", "sell executed @ 1.5406670570373535\n", "buy executed @ 1.5820000171661377\n", "sell executed @ 1.647333025932312\n", "sell executed @ 1.6419999599456787\n", "buy executed @ 1.6493330001831055\n", "sell executed @ 1.600000023841858\n", "buy executed @ 1.5379999876022339\n", "buy executed @ 1.5293329954147339\n", "buy executed @ 1.5893330574035645\n", "sell executed @ 1.5740000009536743\n", "buy executed @ 1.5313329696655273\n", "buy executed @ 1.525333046913147\n", "buy executed @ 1.6053329706192017\n", "sell executed @ 1.6226669549942017\n", "sell executed @ 1.6546670198440552\n", "sell executed @ 1.7200000286102295\n", "sell executed @ 1.718000054359436\n", "buy executed @ 1.7339999675750732\n", "sell executed @ 1.7233330011367798\n", "buy executed @ 1.7086670398712158\n", "buy executed @ 1.7586669921875\n", "sell executed @ 1.7013330459594727\n", "buy executed @ 1.746000051498413\n", "sell executed @ 1.6393330097198486\n", "buy executed @ 1.6080000400543213\n", "buy executed @ 1.6260000467300415\n", "sell executed @ 1.5820000171661377\n", "buy executed @ 1.5773329734802246\n", "buy executed @ 1.6913330554962158\n", "sell executed @ 1.797333002090454\n", "sell executed @ 1.7993329763412476\n", "sell executed @ 1.858667016029358\n", "sell executed @ 1.8406670093536377\n", "buy executed @ 1.8533329963684082\n", "sell executed @ 1.8626669645309448\n", "sell executed @ 1.8700000047683716\n", "sell executed @ 1.8279999494552612\n", "buy executed @ 1.8893330097198486\n", "sell executed @ 1.8380000591278076\n", "buy executed @ 1.8226670026779175\n", "buy executed @ 1.8686670064926147\n", "sell executed @ 1.9033329486846924\n", "sell executed @ 1.8833329677581787\n", "buy executed @ 1.8653329610824585\n", "buy executed @ 1.9173330068588257\n", "sell executed @ 1.9913330078125\n", "sell executed @ 1.9579999446868896\n", "buy executed @ 1.925333023071289\n", "buy executed @ 1.9140000343322754\n", "buy executed @ 2.1640000343322754\n", "sell executed @ 2.1540000438690186\n", "buy executed @ 2.0846669673919678\n", "buy executed @ 2.122667074203491\n", "sell executed @ 2.058666944503784\n", "buy executed @ 2.0886669158935547\n", "sell executed @ 2.2426669597625732\n", "sell executed @ 2.2146670818328857\n", "buy executed @ 2.26200008392334\n", "sell executed @ 2.3293330669403076\n", "sell executed @ 2.245332956314087\n", "buy executed @ 2.173332929611206\n", "buy executed @ 2.117332935333252\n", "buy executed @ 2.138000011444092\n", "sell executed @ 2.0966670513153076\n", "buy executed @ 2.1106669902801514\n", "sell executed @ 2.1706669330596924\n", "sell executed @ 2.1166670322418213\n", "buy executed @ 2.1826670169830322\n", "sell executed @ 2.173332929611206\n", "buy executed @ 2.2200000286102295\n", "sell executed @ 2.2946670055389404\n", "sell executed @ 2.324666976928711\n", "sell executed @ 2.2793331146240234\n", "buy executed @ 2.059333086013794\n", "buy executed @ 2.069333076477051\n", "sell executed @ 2.0273330211639404\n", "buy executed @ 1.963333010673523\n", "buy executed @ 1.9019999504089355\n", "buy executed @ 1.9079999923706055\n", "sell executed @ 1.8666670322418213\n", "buy executed @ 1.850000023841858\n", "buy executed @ 1.8600000143051147\n", "sell executed @ 1.8380000591278076\n", "buy executed @ 1.8513330221176147\n", "sell executed @ 1.8600000143051147\n", "sell executed @ 1.9046670198440552\n", "sell executed @ 1.9006669521331787\n", "buy executed @ 1.9153330326080322\n", "sell executed @ 1.9040000438690186\n", "buy executed @ 1.871999979019165\n", "buy executed @ 1.8473329544067383\n", "buy executed @ 1.8079999685287476\n", "buy executed @ 1.7940000295639038\n", "buy executed @ 1.8166669607162476\n", "sell executed @ 1.841333031654358\n", "sell executed @ 1.8819999694824219\n", "sell executed @ 1.8833329677581787\n", "sell executed @ 1.519333004951477\n", "buy executed @ 1.773332953453064\n", "sell executed @ 1.7873330116271973\n", "sell executed @ 1.784000039100647\n", "buy executed @ 1.773332953453064\n", "buy executed @ 1.7846670150756836\n", "sell executed @ 1.8279999494552612\n", "sell executed @ 1.8646670579910278\n", "sell executed @ 1.929332971572876\n", "sell executed @ 1.9553329944610596\n", "sell executed @ 1.9713330268859863\n", "sell executed @ 1.937999963760376\n", "buy executed @ 1.972000002861023\n", "sell executed @ 2.016666889190674\n", "sell executed @ 2.076667070388794\n", "sell executed @ 2.119999885559082\n", "sell executed @ 2.1066670417785645\n", "buy executed @ 2.128667116165161\n", "sell executed @ 2.171999931335449\n", "sell executed @ 2.0733330249786377\n", "buy executed @ 2.0993330478668213\n", "sell executed @ 2.2113330364227295\n", "sell executed @ 2.240000009536743\n", "sell executed @ 2.2786669731140137\n", "sell executed @ 2.3313329219818115\n", "sell executed @ 2.299999952316284\n", "buy executed @ 2.2813329696655273\n", "buy executed @ 2.302000045776367\n", "sell executed @ 2.25\n", "buy executed @ 2.2413330078125\n", "buy executed @ 2.253999948501587\n", "sell executed @ 2.2273330688476562\n", "buy executed @ 2.2939999103546143\n", "sell executed @ 2.2693328857421875\n", "buy executed @ 2.251332998275757\n", "buy executed @ 2.2073330879211426\n", "buy executed @ 2.2079999446868896\n", "sell executed @ 2.204667091369629\n", "buy executed @ 2.315999984741211\n", "sell executed @ 2.4006669521331787\n", "sell executed @ 2.4059998989105225\n", "sell executed @ 2.3526670932769775\n", "buy executed @ 2.3333330154418945\n", "buy executed @ 2.3546669483184814\n", "sell executed @ 2.3320000171661377\n", "buy executed @ 2.330667018890381\n", "buy executed @ 2.3433330059051514\n", "sell executed @ 2.293333053588867\n", "buy executed @ 2.2720000743865967\n", "buy executed @ 2.493333101272583\n", "sell executed @ 2.5293331146240234\n", "sell executed @ 2.5233330726623535\n", "buy executed @ 2.4886670112609863\n", "buy executed @ 2.4826669692993164\n", "buy executed @ 2.438667058944702\n", "buy executed @ 2.5339999198913574\n", "sell executed @ 2.3333330154418945\n", "buy executed @ 2.2986669540405273\n", "buy executed @ 2.2100000381469727\n", "buy executed @ 2.1640000343322754\n", "buy executed @ 2.2060000896453857\n", "sell executed @ 2.22933292388916\n", "sell executed @ 2.239332914352417\n", "sell executed @ 2.1500000953674316\n", "buy executed @ 2.1493330001831055\n", "buy executed @ 2.177333116531372\n", "sell executed @ 2.2106668949127197\n", "sell executed @ 2.2106668949127197\n", "buy executed @ 2.121332883834839\n", "buy executed @ 2.194000005722046\n", "sell executed @ 2.2326669692993164\n", "sell executed @ 2.2226669788360596\n", "buy executed @ 2.208667039871216\n", "buy executed @ 2.252000093460083\n", "sell executed @ 2.262666940689087\n", "sell executed @ 2.1640000343322754\n", "buy executed @ 2.121999979019165\n", "buy executed @ 2.1646668910980225\n", "sell executed @ 2.012666940689087\n", "buy executed @ 2.003999948501587\n", "buy executed @ 2.1973330974578857\n", "sell executed @ 2.1500000953674316\n", "buy executed @ 2.003999948501587\n", "buy executed @ 1.9620000123977661\n", "buy executed @ 1.9453330039978027\n", "buy executed @ 1.9046670198440552\n", "buy executed @ 1.8373329639434814\n", "buy executed @ 1.9179999828338623\n", "sell executed @ 2.053333044052124\n", "sell executed @ 2.068000078201294\n", "sell executed @ 2.018666982650757\n", "buy executed @ 1.987333059310913\n", "buy executed @ 2.1126670837402344\n", "sell executed @ 2.0273330211639404\n", "buy executed @ 1.9666670560836792\n", "buy executed @ 1.8766670227050781\n", "buy executed @ 1.858667016029358\n", "buy executed @ 1.8606669902801514\n", "sell executed @ 1.9479999542236328\n", "sell executed @ 1.9286669492721558\n", "buy executed @ 2.0053329467773438\n", "sell executed @ 1.9413330554962158\n", "buy executed @ 1.9773329496383667\n", "sell executed @ 1.9846669435501099\n", "sell executed @ 1.9593329429626465\n", "buy executed @ 1.99399995803833\n", "sell executed @ 2.122667074203491\n", "sell executed @ 2.1393330097198486\n", "sell executed @ 2.252000093460083\n", "sell executed @ 2.1459999084472656\n", "buy executed @ 2.25266695022583\n", "sell executed @ 2.2073330879211426\n", "buy executed @ 2.107332944869995\n", "buy executed @ 2.130666971206665\n", "sell executed @ 2.0940001010894775\n", "buy executed @ 2.0859999656677246\n", "buy executed @ 2.0266671180725098\n", "buy executed @ 2.0439999103546143\n", "sell executed @ 2.0820000171661377\n", "sell executed @ 2.065999984741211\n", "buy executed @ 2.0993330478668213\n", "sell executed @ 2.0846669673919678\n", "buy executed @ 2.1006669998168945\n", "sell executed @ 2.180000066757202\n", "sell executed @ 2.2833330631256104\n", "sell executed @ 2.3973329067230225\n", "sell executed @ 2.2233328819274902\n", "buy executed @ 2.1433329582214355\n", "buy executed @ 2.1513330936431885\n", "sell executed @ 2.119333028793335\n", "buy executed @ 2.0439999103546143\n", "buy executed @ 1.9893330335617065\n", "buy executed @ 1.9299999475479126\n", "buy executed @ 1.8753329515457153\n", "buy executed @ 1.9673329591751099\n", "sell executed @ 1.8233330249786377\n", "buy executed @ 1.8279999494552612\n", "sell executed @ 1.75\n", "buy executed @ 1.7400000095367432\n", "buy executed @ 1.8179999589920044\n", "sell executed @ 1.8846670389175415\n", "sell executed @ 2.016666889190674\n", "sell executed @ 1.9393329620361328\n", "buy executed @ 1.9606670141220093\n", "sell executed @ 1.996000051498413\n", "sell executed @ 2.078000068664551\n", "sell executed @ 1.9613330364227295\n", "buy executed @ 1.9600000381469727\n", "buy executed @ 2.0199999809265137\n", "sell executed @ 2.000667095184326\n", "buy executed @ 1.9673329591751099\n", "buy executed @ 1.9406670331954956\n", "buy executed @ 1.9966670274734497\n", "sell executed @ 2.0486669540405273\n", "sell executed @ 1.9666670560836792\n", "buy executed @ 1.8880000114440918\n", "buy executed @ 1.9126670360565186\n", "sell executed @ 1.8940000534057617\n", "buy executed @ 1.8940000534057617\n", "sell executed @ 1.8760000467300415\n", "buy executed @ 1.8626669645309448\n", "buy executed @ 1.9033329486846924\n", "sell executed @ 1.9566669464111328\n", "sell executed @ 1.824666976928711\n", "buy executed @ 1.8533329963684082\n", "sell executed @ 1.8853329420089722\n", "sell executed @ 1.9653329849243164\n", "sell executed @ 2.0260000228881836\n", "sell executed @ 2.169332981109619\n", "sell executed @ 2.0893330574035645\n", "buy executed @ 2.069999933242798\n", "buy executed @ 2.059999942779541\n", "buy executed @ 2.001332998275757\n", "buy executed @ 2.0439999103546143\n", "sell executed @ 1.843999981880188\n", "buy executed @ 1.8359999656677246\n", "buy executed @ 1.8993330001831055\n", "sell executed @ 1.9520000219345093\n", "sell executed @ 1.944000005722046\n", "buy executed @ 1.9866670370101929\n", "sell executed @ 1.9533330202102661\n", "buy executed @ 1.9600000381469727\n", "sell executed @ 1.9259999990463257\n", "buy executed @ 1.9500000476837158\n", "sell executed @ 1.891332983970642\n", "buy executed @ 1.8933329582214355\n", "sell executed @ 1.8880000114440918\n", "buy executed @ 1.8426669836044312\n", "buy executed @ 1.8220000267028809\n", "buy executed @ 1.8706669807434082\n", "sell executed @ 1.9213329553604126\n", "sell executed @ 1.869333028793335\n", "buy executed @ 1.8493330478668213\n", "buy executed @ 1.8566670417785645\n", "sell executed @ 1.8926670551300049\n", "sell executed @ 1.8279999494552612\n", "buy executed @ 1.8346669673919678\n", "sell executed @ 1.8253329992294312\n", "buy executed @ 1.8753329515457153\n", "sell executed @ 1.9500000476837158\n", "sell executed @ 1.9279999732971191\n", "buy executed @ 2.0999999046325684\n", "sell executed @ 2.076667070388794\n", "buy executed @ 2.1026670932769775\n", "sell executed @ 2.0873329639434814\n", "buy executed @ 2.0213329792022705\n", "buy executed @ 2.0713329315185547\n", "sell executed @ 2.107332944869995\n", "sell executed @ 2.0920000076293945\n", "buy executed @ 2.0546669960021973\n", "buy executed @ 2.122667074203491\n", "sell executed @ 2.194667100906372\n", "sell executed @ 2.200000047683716\n", "sell executed @ 2.1646668910980225\n", "buy executed @ 2.1419999599456787\n", "buy executed @ 2.1513330936431885\n", "sell executed @ 2.1433329582214355\n", "buy executed @ 2.2153329849243164\n", "sell executed @ 2.246000051498413\n", "sell executed @ 2.254667043685913\n", "sell executed @ 2.308000087738037\n", "sell executed @ 2.259999990463257\n", "buy executed @ 2.24733304977417\n", "buy executed @ 2.259999990463257\n", "sell executed @ 2.2780001163482666\n", "sell executed @ 2.3046669960021973\n", "sell executed @ 2.3519999980926514\n", "sell executed @ 2.3506669998168945\n", "buy executed @ 2.2406671047210693\n", "buy executed @ 2.253999948501587\n", "sell executed @ 2.293333053588867\n", "sell executed @ 2.305999994277954\n", "sell executed @ 2.307332992553711\n", "sell executed @ 2.295332908630371\n", "buy executed @ 2.266666889190674\n", "buy executed @ 2.2853329181671143\n", "sell executed @ 2.239332914352417\n", "buy executed @ 2.246000051498413\n", "sell executed @ 2.2146670818328857\n", "buy executed @ 2.257999897003174\n", "sell executed @ 2.357332944869995\n", "sell executed @ 2.318000078201294\n", "buy executed @ 2.293333053588867\n", "buy executed @ 2.2893331050872803\n", "buy executed @ 2.245332956314087\n", "buy executed @ 2.2426669597625732\n", "buy executed @ 2.23533296585083\n", "buy executed @ 2.194000005722046\n", "buy executed @ 2.2173330783843994\n", "sell executed @ 2.259999990463257\n", "sell executed @ 2.2733330726623535\n", "sell executed @ 2.2920000553131104\n", "sell executed @ 2.301332950592041\n", "sell executed @ 2.3459999561309814\n", "sell executed @ 2.4000000953674316\n", "sell executed @ 2.4660000801086426\n", "sell executed @ 2.4653329849243164\n", "buy executed @ 2.5353329181671143\n", "sell executed @ 2.5299999713897705\n", "buy executed @ 2.501332998275757\n", "buy executed @ 2.500667095184326\n", "buy executed @ 2.553333044052124\n", "sell executed @ 2.5160000324249268\n", "buy executed @ 2.5420000553131104\n", "sell executed @ 2.611332893371582\n", "sell executed @ 2.631999969482422\n", "sell executed @ 2.615999937057495\n", "buy executed @ 2.561332941055298\n", "buy executed @ 2.5260000228881836\n", "buy executed @ 2.563333034515381\n", "sell executed @ 2.551332950592041\n", "buy executed @ 2.4693329334259033\n", "buy executed @ 2.618666887283325\n", "sell executed @ 2.569333076477051\n", "buy executed @ 2.3440001010894775\n", "buy executed @ 2.4073328971862793\n", "sell executed @ 2.2920000553131104\n", "buy executed @ 2.295332908630371\n", "sell executed @ 2.3399999141693115\n", "sell executed @ 2.322000026702881\n", "buy executed @ 2.309999942779541\n", "buy executed @ 2.371999979019165\n", "sell executed @ 2.4433329105377197\n", "sell executed @ 2.512666940689087\n", "sell executed @ 2.5486669540405273\n", "sell executed @ 2.564666986465454\n", "sell executed @ 2.6066670417785645\n", "sell executed @ 2.6080000400543213\n", "sell executed @ 2.5986669063568115\n", "buy executed @ 2.456666946411133\n", "buy executed @ 2.3526670932769775\n", "buy executed @ 2.3433330059051514\n", "buy executed @ 2.3386669158935547\n", "buy executed @ 2.396667003631592\n", "sell executed @ 2.4006669521331787\n", "sell executed @ 2.441333055496216\n", "sell executed @ 2.502000093460083\n", "sell executed @ 2.5239999294281006\n", "sell executed @ 2.5439999103546143\n", "sell executed @ 2.5260000228881836\n", "buy executed @ 2.9286670684814453\n", "sell executed @ 2.9560000896453857\n", "sell executed @ 2.740000009536743\n", "buy executed @ 2.8006670475006104\n", "sell executed @ 2.757999897003174\n", "buy executed @ 2.7886669635772705\n", "sell executed @ 2.700000047683716\n", "buy executed @ 2.7906670570373535\n", "sell executed @ 2.9059998989105225\n", "sell executed @ 2.9166669845581055\n", "sell executed @ 2.886667013168335\n", "buy executed @ 3.0393331050872803\n", "sell executed @ 3.0299999713897705\n", "buy executed @ 3.131333112716675\n", "sell executed @ 3.188667058944702\n", "sell executed @ 3.3459999561309814\n", "sell executed @ 3.4006669521331787\n", "sell executed @ 3.361999988555908\n", "buy executed @ 3.4666669368743896\n", "sell executed @ 3.413332939147949\n", "buy executed @ 3.6626670360565186\n", "sell executed @ 3.5993330478668213\n", "buy executed @ 3.552000045776367\n", "buy executed @ 3.607332944869995\n", "sell executed @ 3.636667013168335\n", "sell executed @ 3.9666669368743896\n", "sell executed @ 3.700666904449463\n", "buy executed @ 3.7193329334259033\n", "sell executed @ 4.626667022705078\n", "sell executed @ 5.117332935333252\n", "sell executed @ 5.853332996368408\n", "sell executed @ 5.549333095550537\n", "buy executed @ 5.656000137329102\n", "sell executed @ 6.150000095367432\n", "sell executed @ 6.099999904632568\n", "buy executed @ 5.995999813079834\n", "buy executed @ 5.8393330574035645\n", "buy executed @ 5.815999984741211\n", "buy executed @ 6.182000160217285\n", "sell executed @ 6.4720001220703125\n", "sell executed @ 7.355332851409912\n", "sell executed @ 6.975333213806152\n", "buy executed @ 6.99666690826416\n", "sell executed @ 6.517333030700684\n", "buy executed @ 6.172667026519775\n", "buy executed @ 6.322667121887207\n", "sell executed @ 6.357999801635742\n", "sell executed @ 6.489999771118164\n", "sell executed @ 6.802667140960693\n", "sell executed @ 6.670000076293945\n", "buy executed @ 6.297999858856201\n", "buy executed @ 6.51533317565918\n", "sell executed @ 6.545332908630371\n", "sell executed @ 6.686666965484619\n", "sell executed @ 6.813333034515381\n", "sell executed @ 6.892666816711426\n", "sell executed @ 6.97866678237915\n", "sell executed @ 6.710000038146973\n", "buy executed @ 6.636666774749756\n", "buy executed @ 6.765999794006348\n", "sell executed @ 6.826666831970215\n", "sell executed @ 7.047999858856201\n", "sell executed @ 7.283332824707031\n", "sell executed @ 7.157332897186279\n", "buy executed @ 7.811999797821045\n", "sell executed @ 7.8546671867370605\n", "sell executed @ 7.682666778564453\n", "buy executed @ 8.005999565124512\n", "sell executed @ 8.107333183288574\n", "sell executed @ 8.229999542236328\n", "sell executed @ 8.15133285522461\n", "buy executed @ 8.37399959564209\n", "sell executed @ 8.65999984741211\n", "sell executed @ 8.484000205993652\n", "buy executed @ 7.269999980926514\n", "buy executed @ 8.016667366027832\n", "sell executed @ 7.935332775115967\n", "buy executed @ 7.97866678237915\n", "sell executed @ 8.161999702453613\n", "sell executed @ 8.182666778564453\n", "sell executed @ 8.113332748413086\n", "buy executed @ 8.271332740783691\n", "sell executed @ 8.62600040435791\n", "sell executed @ 8.974666595458984\n", "sell executed @ 8.78266716003418\n", "buy executed @ 8.95199966430664\n", "sell executed @ 9.036666870117188\n", "sell executed @ 9.199999809265137\n", "sell executed @ 9.645333290100098\n", "sell executed @ 9.476667404174805\n", "buy executed @ 8.9486665725708\n", "buy executed @ 10.232000350952148\n", "sell executed @ 10.199999809265137\n", "buy executed @ 9.825332641601562\n", "buy executed @ 9.695332527160645\n", "buy executed @ 9.290666580200195\n", "buy executed @ 9.311332702636719\n", "sell executed @ 9.466667175292969\n", "sell executed @ 9.65999984741211\n", "sell executed @ 9.972000122070312\n", "sell executed @ 9.857333183288574\n", "buy executed @ 10.473333358764648\n", "sell executed @ 10.78933334350586\n", "sell executed @ 10.947999954223633\n", "sell executed @ 11.133999824523926\n", "sell executed @ 11.096667289733887\n", "buy executed @ 11.070667266845703\n", "buy executed @ 11.266667366027832\n", "sell executed @ 11.262666702270508\n", "buy executed @ 11.374667167663574\n", "sell executed @ 11.328666687011719\n", "buy executed @ 11.131333351135254\n", "buy executed @ 10.713333129882812\n", "buy executed @ 11.091333389282227\n", "sell executed @ 10.90133285522461\n", "buy executed @ 10.995332717895508\n", "sell executed @ 11.03600025177002\n", "sell executed @ 11.10533332824707\n", "sell executed @ 11.081999778747559\n", "buy executed @ 11.08133316040039\n", "buy executed @ 11.861332893371582\n", "sell executed @ 12.22599983215332\n", "sell executed @ 12.074000358581543\n", "buy executed @ 12.155332565307617\n", "sell executed @ 12.349332809448242\n", "sell executed @ 12.576000213623047\n", "sell executed @ 12.726667404174805\n", "sell executed @ 12.891332626342773\n", "sell executed @ 12.866666793823242\n", "buy executed @ 12.063332557678223\n", "buy executed @ 11.553999900817871\n", "buy executed @ 12.065333366394043\n", "sell executed @ 12.204667091369629\n", "sell executed @ 11.648667335510254\n", "buy executed @ 11.251999855041504\n", "buy executed @ 11.528667449951172\n", "sell executed @ 11.91333293914795\n", "sell executed @ 11.981332778930664\n", "sell executed @ 12.262666702270508\n", "sell executed @ 12.237333297729492\n", "buy executed @ 12.186667442321777\n", "buy executed @ 12.226667404174805\n", "sell executed @ 11.506667137145996\n", "buy executed @ 11.435999870300293\n", "buy executed @ 10.966667175292969\n", "buy executed @ 11.543333053588867\n", "sell executed @ 11.310667037963867\n", "buy executed @ 10.857333183288574\n", "buy executed @ 10.964667320251465\n", "sell executed @ 10.614666938781738\n", "buy executed @ 10.662667274475098\n", "sell executed @ 10.811332702636719\n", "sell executed @ 11.680000305175781\n", "sell executed @ 11.787332534790039\n", "sell executed @ 10.077333450317383\n", "buy executed @ 9.317999839782715\n", "buy executed @ 9.196666717529297\n", "buy executed @ 9.646666526794434\n", "sell executed @ 9.186667442321777\n", "buy executed @ 9.24666690826416\n", "sell executed @ 9.173333168029785\n", "buy executed @ 9.029999732971191\n", "buy executed @ 8.10533332824707\n", "buy executed @ 8.406000137329102\n", "sell executed @ 8.074000358581543\n", "buy executed @ 8.140000343322754\n", "sell executed @ 8.092000007629395\n", "buy executed @ 8.055999755859375\n", "buy executed @ 8.033332824707031\n", "buy executed @ 8.462667465209961\n", "sell executed @ 8.485333442687988\n", "sell executed @ 8.277999877929688\n", "buy executed @ 9.646666526794434\n", "sell executed @ 9.263333320617676\n", "buy executed @ 9.36533260345459\n", "sell executed @ 9.157333374023438\n", "buy executed @ 9.4399995803833\n", "sell executed @ 9.47933292388916\n", "sell executed @ 9.3100004196167\n", "buy executed @ 9.83133316040039\n", "sell executed @ 9.84333324432373\n", "sell executed @ 9.862667083740234\n", "sell executed @ 10.163999557495117\n", "sell executed @ 9.86533260345459\n", "buy executed @ 9.381333351135254\n", "buy executed @ 9.549332618713379\n", "sell executed @ 9.569999694824219\n", "sell executed @ 10.093999862670898\n", "sell executed @ 10.366666793823242\n", "sell executed @ 10.074666976928711\n", "buy executed @ 10.162667274475098\n", "sell executed @ 10.028667449951172\n", "buy executed @ 10.006667137145996\n", "buy executed @ 9.970666885375977\n", "buy executed @ 9.800000190734863\n", "buy executed @ 9.957332611083984\n", "sell executed @ 10.085332870483398\n", "sell executed @ 9.835332870483398\n", "buy executed @ 9.714667320251465\n", "buy executed @ 9.28933334350586\n", "buy executed @ 10.751333236694336\n", "sell executed @ 10.942000389099121\n", "sell executed @ 11.39799976348877\n", "sell executed @ 11.333999633789062\n", "buy executed @ 11.778667449951172\n", "sell executed @ 11.904000282287598\n", "sell executed @ 12.100000381469727\n", "sell executed @ 11.640000343322754\n", "buy executed @ 11.307999610900879\n", "buy executed @ 11.892000198364258\n", "sell executed @ 11.682000160217285\n", "buy executed @ 12.189332962036133\n", "sell executed @ 12.093999862670898\n", "buy executed @ 11.807332992553711\n", "buy executed @ 11.915332794189453\n", "sell executed @ 11.628000259399414\n", "buy executed @ 11.892000198364258\n", "sell executed @ 12.435333251953125\n", "sell executed @ 13.104000091552734\n", "sell executed @ 13.107999801635742\n", "sell executed @ 13.021332740783691\n", "buy executed @ 13.308667182922363\n", "sell executed @ 13.215332984924316\n", "buy executed @ 13.579999923706055\n", "sell executed @ 12.909333229064941\n", "buy executed @ 13.998000144958496\n", "sell executed @ 13.973333358764648\n", "buy executed @ 14.510000228881836\n", "sell executed @ 16.53333282470703\n", "sell executed @ 16.866666793823242\n", "sell executed @ 16.836000442504883\n", "buy executed @ 16.320667266845703\n", "buy executed @ 16.70400047302246\n", "sell executed @ 16.98933219909668\n", "sell executed @ 16.8439998626709\n", "buy executed @ 16.862667083740234\n", "sell executed @ 16.413999557495117\n", "buy executed @ 15.922666549682617\n", "buy executed @ 15.62733268737793\n", "buy executed @ 16.099332809448242\n", "sell executed @ 15.852666854858398\n", "buy executed @ 15.39799976348877\n", "buy executed @ 15.59866714477539\n", "sell executed @ 16.002666473388672\n", "sell executed @ 15.72266674041748\n", "buy executed @ 15.660667419433594\n", "buy executed @ 15.259332656860352\n", "buy executed @ 14.678000450134277\n", "buy executed @ 14.696000099182129\n", "sell executed @ 14.197333335876465\n", "buy executed @ 13.821332931518555\n", "buy executed @ 14.157999992370605\n", "sell executed @ 13.896666526794434\n", "buy executed @ 14.464667320251465\n", "sell executed @ 15.352666854858398\n", "sell executed @ 15.026666641235352\n", "buy executed @ 14.148667335510254\n", "buy executed @ 13.834667205810547\n", "buy executed @ 14.36400032043457\n", "sell executed @ 14.461999893188477\n", "sell executed @ 13.612667083740234\n", "buy executed @ 13.585332870483398\n", "buy executed @ 13.206000328063965\n", "buy executed @ 12.927332878112793\n", "buy executed @ 13.27400016784668\n", "sell executed @ 13.208000183105469\n", "buy executed @ 13.625332832336426\n", "sell executed @ 14.576000213623047\n", "sell executed @ 13.866000175476074\n", "buy executed @ 13.857333183288574\n", "buy executed @ 13.323332786560059\n", "buy executed @ 13.234000205993652\n", "buy executed @ 13.79466724395752\n", "sell executed @ 13.859333038330078\n", "sell executed @ 13.84866714477539\n", "buy executed @ 14.060667037963867\n", "sell executed @ 14.440667152404785\n", "sell executed @ 13.8186674118042\n", "buy executed @ 13.423333168029785\n", "buy executed @ 11.906000137329102\n", "buy executed @ 12.150667190551758\n", "sell executed @ 12.311332702636719\n", "sell executed @ 12.677332878112793\n", "sell executed @ 12.708000183105469\n", "sell executed @ 12.572667121887207\n", "buy executed @ 12.77066707611084\n", "sell executed @ 13.072667121887207\n", "sell executed @ 13.020000457763672\n", "buy executed @ 13.296667098999023\n", "sell executed @ 13.658666610717773\n", "sell executed @ 13.819999694824219\n", "sell executed @ 14.104000091552734\n", "sell executed @ 14.015999794006348\n", "buy executed @ 14.015999794006348\n", "buy executed @ 13.646666526794434\n", "buy executed @ 13.662667274475098\n", "sell executed @ 13.599332809448242\n", "buy executed @ 13.793333053588867\n", "sell executed @ 13.878000259399414\n", "sell executed @ 13.687333106994629\n", "buy executed @ 13.486666679382324\n", "buy executed @ 13.631333351135254\n", "sell executed @ 13.567999839782715\n", "buy executed @ 13.761333465576172\n", "sell executed @ 14.973999977111816\n", "sell executed @ 15.444666862487793\n", "sell executed @ 15.141332626342773\n", "buy executed @ 15.185999870300293\n", "sell executed @ 15.305999755859375\n", "sell executed @ 15.814666748046875\n", "sell executed @ 15.5\n", "buy executed @ 15.792667388916016\n", "sell executed @ 15.706666946411133\n", "buy executed @ 15.937333106994629\n", "sell executed @ 16.003999710083008\n", "sell executed @ 15.981332778930664\n", "buy executed @ 15.295332908630371\n", "buy executed @ 15.283332824707031\n", "buy executed @ 14.843999862670898\n", "buy executed @ 14.604666709899902\n", "buy executed @ 14.870667457580566\n", "sell executed @ 14.630666732788086\n", "buy executed @ 14.541999816894531\n", "buy executed @ 15.113332748413086\n", "sell executed @ 14.638667106628418\n", "buy executed @ 14.477333068847656\n", "buy executed @ 14.359999656677246\n", "buy executed @ 14.668000221252441\n", "sell executed @ 14.702667236328125\n", "sell executed @ 14.638667106628418\n", "buy executed @ 14.832667350769043\n", "sell executed @ 14.902667045593262\n", "sell executed @ 14.904666900634766\n", "sell executed @ 14.98799991607666\n", "sell executed @ 15.000666618347168\n", "sell executed @ 15.261333465576172\n", "sell executed @ 14.886667251586914\n", "buy executed @ 15.5513334274292\n", "sell executed @ 15.90133285522461\n", "sell executed @ 15.899333000183105\n", "buy executed @ 16.595333099365234\n", "sell executed @ 16.826000213623047\n", "sell executed @ 16.54199981689453\n", "buy executed @ 17.288000106811523\n", "sell executed @ 17.33066749572754\n", "sell executed @ 17.354000091552734\n", "sell executed @ 17.42533302307129\n", "sell executed @ 17.46733283996582\n", "sell executed @ 17.32933235168457\n", "buy executed @ 17.117332458496094\n", "buy executed @ 17.047332763671875\n", "buy executed @ 16.95599937438965\n", "buy executed @ 17.118667602539062\n", "sell executed @ 17.503332138061523\n", "sell executed @ 17.44933319091797\n", "buy executed @ 17.549999237060547\n", "sell executed @ 17.590667724609375\n", "sell executed @ 17.979999542236328\n", "sell executed @ 18.941333770751953\n", "sell executed @ 18.746000289916992\n", "buy executed @ 19.069332122802734\n", "sell executed @ 18.492666244506836\n", "buy executed @ 18.80733299255371\n", "sell executed @ 18.565332412719727\n", "buy executed @ 18.739999771118164\n", "sell executed @ 18.687332153320312\n", "buy executed @ 18.613332748413086\n", "buy executed @ 16.923999786376953\n", "buy executed @ 17.382667541503906\n", "sell executed @ 17.42533302307129\n", "sell executed @ 17.58799934387207\n", "sell executed @ 17.288000106811523\n", "buy executed @ 16.66866683959961\n", "buy executed @ 16.694000244140625\n", "sell executed @ 16.80933380126953\n", "sell executed @ 16.463333129882812\n", "buy executed @ 16.440000534057617\n", "buy executed @ 16.35066795349121\n", "buy executed @ 16.178667068481445\n", "buy executed @ 16.016000747680664\n", "buy executed @ 16.761333465576172\n", "sell executed @ 17.013999938964844\n", "sell executed @ 17.374666213989258\n", "sell executed @ 17.30466651916504\n", "buy executed @ 17.28533363342285\n", "buy executed @ 17.134000778198242\n", "buy executed @ 15.793999671936035\n", "buy executed @ 14.97266674041748\n", "buy executed @ 15.137332916259766\n", "sell executed @ 15.313332557678223\n", "sell executed @ 15.09000015258789\n", "buy executed @ 15.165332794189453\n", "sell executed @ 15.364666938781738\n", "sell executed @ 15.689332962036133\n", "sell executed @ 15.40666675567627\n", "buy executed @ 15.685999870300293\n", "sell executed @ 15.682666778564453\n", "buy executed @ 14.777999877929688\n", "buy executed @ 16.184667587280273\n", "sell executed @ 15.873332977294922\n", "buy executed @ 15.910667419433594\n", "sell executed @ 16.113332748413086\n", "sell executed @ 16.172666549682617\n", "sell executed @ 15.928667068481445\n", "buy executed @ 15.39799976348877\n", "buy executed @ 16.08133316040039\n", "sell executed @ 16.01333236694336\n", "buy executed @ 16.1286678314209\n", "sell executed @ 16.738666534423828\n", "sell executed @ 16.606666564941406\n", "buy executed @ 16.780000686645508\n", "sell executed @ 17.245332717895508\n", "sell executed @ 16.93199920654297\n", "buy executed @ 17.18000030517578\n", "sell executed @ 16.516000747680664\n", "buy executed @ 16.58066749572754\n", "sell executed @ 16.185333251953125\n", "buy executed @ 16.447999954223633\n", "sell executed @ 16.53933334350586\n", "sell executed @ 16.562667846679688\n", "sell executed @ 16.301332473754883\n", "buy executed @ 15.442667007446289\n", "buy executed @ 15.428667068481445\n", "buy executed @ 15.286666870117188\n", "buy executed @ 15.218667030334473\n", "buy executed @ 14.913999557495117\n", "buy executed @ 14.290666580200195\n", "buy executed @ 14.459333419799805\n", "sell executed @ 13.989333152770996\n", "buy executed @ 13.925333023071289\n", "buy executed @ 13.800000190734863\n", "buy executed @ 13.602666854858398\n", "buy executed @ 13.187333106994629\n", "buy executed @ 13.721332550048828\n", "sell executed @ 14.550666809082031\n", "sell executed @ 14.619333267211914\n", "sell executed @ 14.84000015258789\n", "sell executed @ 14.731332778930664\n", "buy executed @ 14.817333221435547\n", "sell executed @ 15.187999725341797\n", "sell executed @ 15.047332763671875\n", "buy executed @ 14.815333366394043\n", "buy executed @ 14.827333450317383\n", "sell executed @ 14.620667457580566\n", "buy executed @ 14.005999565124512\n", "buy executed @ 14.085332870483398\n", "sell executed @ 14.063332557678223\n", "buy executed @ 14.041333198547363\n", "buy executed @ 13.77733325958252\n", "buy executed @ 13.480667114257812\n", "buy executed @ 13.616666793823242\n", "sell executed @ 12.845999717712402\n", "buy executed @ 12.791333198547363\n", "buy executed @ 12.871333122253418\n", "sell executed @ 12.795332908630371\n", "buy executed @ 13.104666709899902\n", "sell executed @ 13.441332817077637\n", "sell executed @ 13.419333457946777\n", "buy executed @ 13.770000457763672\n", "sell executed @ 13.732000350952148\n", "buy executed @ 13.291333198547363\n", "buy executed @ 13.680000305175781\n", "sell executed @ 13.573332786560059\n", "buy executed @ 14.062666893005371\n", "sell executed @ 14.557332992553711\n", "sell executed @ 14.569999694824219\n", "sell executed @ 14.732666969299316\n", "sell executed @ 14.490667343139648\n", "buy executed @ 14.498666763305664\n", "sell executed @ 14.419333457946777\n", "buy executed @ 14.186667442321777\n", "buy executed @ 13.525333404541016\n", "buy executed @ 13.584667205810547\n", "sell executed @ 13.623332977294922\n", "sell executed @ 13.630666732788086\n", "sell executed @ 14.11400032043457\n", "sell executed @ 14.473999977111816\n", "sell executed @ 13.822667121887207\n", "buy executed @ 13.607333183288574\n", "buy executed @ 13.583999633789062\n", "buy executed @ 13.812666893005371\n", "sell executed @ 13.555999755859375\n", "buy executed @ 13.155332565307617\n", "buy executed @ 13.303999900817871\n", "sell executed @ 13.496000289916992\n", "sell executed @ 13.375332832336426\n", "buy executed @ 12.925333023071289\n", "buy executed @ 12.725333213806152\n", "buy executed @ 12.687999725341797\n", "buy executed @ 12.916000366210938\n", "sell executed @ 12.73799991607666\n", "buy executed @ 12.578666687011719\n", "buy executed @ 13.046667098999023\n", "sell executed @ 12.982000350952148\n", "buy executed @ 13.380666732788086\n", "sell executed @ 13.043333053588867\n", "buy executed @ 13.20533275604248\n", "sell executed @ 13.308667182922363\n", "sell executed @ 13.447999954223633\n", "sell executed @ 12.953332901000977\n", "buy executed @ 12.694000244140625\n", "buy executed @ 12.333333015441895\n", "buy executed @ 12.704667091369629\n", "sell executed @ 12.584667205810547\n", "buy executed @ 12.505999565124512\n", "buy executed @ 12.733332633972168\n", "sell executed @ 13.539999961853027\n", "sell executed @ 13.550000190734863\n", "sell executed @ 13.844667434692383\n", "sell executed @ 14.005999565124512\n", "sell executed @ 14.0600004196167\n", "sell executed @ 13.985333442687988\n", "buy executed @ 13.830666542053223\n", "buy executed @ 13.85533332824707\n", "sell executed @ 13.779999732971191\n", "buy executed @ 13.78600025177002\n", "sell executed @ 13.684666633605957\n", "buy executed @ 13.96066665649414\n", "sell executed @ 14.629332542419434\n", "sell executed @ 14.573332786560059\n", "buy executed @ 14.562000274658203\n", "buy executed @ 15.436667442321777\n", "sell executed @ 15.36533260345459\n", "buy executed @ 15.49666690826416\n", "sell executed @ 15.069999694824219\n", "buy executed @ 15.0686674118042\n", "buy executed @ 15.36733341217041\n", "sell executed @ 15.529999732971191\n", "sell executed @ 15.362000465393066\n", "buy executed @ 15.786666870117188\n", "sell executed @ 15.77400016784668\n", "buy executed @ 15.965999603271484\n", "sell executed @ 16.31599998474121\n", "sell executed @ 16.211999893188477\n", "buy executed @ 16.273332595825195\n", "sell executed @ 16.589332580566406\n", "sell executed @ 16.583332061767578\n", "buy executed @ 16.47599983215332\n", "buy executed @ 16.290000915527344\n", "buy executed @ 16.374666213989258\n", "sell executed @ 16.51533317565918\n", "sell executed @ 16.497333526611328\n", "buy executed @ 16.495332717895508\n", "buy executed @ 16.76333236694336\n", "sell executed @ 16.719999313354492\n", "buy executed @ 16.6299991607666\n", "buy executed @ 16.55666732788086\n", "buy executed @ 16.599332809448242\n", "sell executed @ 16.39466667175293\n", "buy executed @ 16.609333038330078\n", "sell executed @ 17.086000442504883\n", "sell executed @ 17.066667556762695\n", "buy executed @ 16.713333129882812\n", "buy executed @ 16.76066780090332\n", "sell executed @ 16.71266746520996\n", "buy executed @ 16.691999435424805\n", "buy executed @ 16.874666213989258\n", "sell executed @ 17.360666275024414\n", "sell executed @ 17.459333419799805\n", "sell executed @ 17.500667572021484\n", "sell executed @ 17.319332122802734\n", "buy executed @ 17.844667434692383\n", "sell executed @ 17.67799949645996\n", "buy executed @ 17.91933250427246\n", "sell executed @ 17.805999755859375\n", "buy executed @ 17.468000411987305\n", "buy executed @ 17.884000778198242\n", "sell executed @ 17.94333267211914\n", "sell executed @ 18.667999267578125\n", "sell executed @ 18.648000717163086\n", "buy executed @ 17.858667373657227\n", "buy executed @ 16.997333526611328\n", "buy executed @ 17.19466781616211\n", "sell executed @ 17.27666664123535\n", "sell executed @ 17.477333068847656\n", "sell executed @ 17.709999084472656\n", "sell executed @ 17.542667388916016\n", "buy executed @ 17.778667449951172\n", "sell executed @ 18.310667037963867\n", "sell executed @ 18.817333221435547\n", "sell executed @ 17.784666061401367\n", "buy executed @ 17.857999801635742\n", "sell executed @ 17.81333351135254\n", "buy executed @ 17.694000244140625\n", "buy executed @ 16.867332458496094\n", "buy executed @ 17.654666900634766\n", "sell executed @ 17.58799934387207\n", "buy executed @ 17.785999298095703\n", "sell executed @ 17.74333381652832\n", "buy executed @ 17.332666397094727\n", "buy executed @ 17.75200080871582\n", "sell executed @ 18.0086669921875\n", "sell executed @ 16.408666610717773\n", "buy executed @ 16.167333602905273\n", "buy executed @ 16.076000213623047\n", "buy executed @ 15.824666976928711\n", "buy executed @ 15.878000259399414\n", "sell executed @ 16.167333602905273\n", "sell executed @ 16.209999084472656\n", "sell executed @ 16.999332427978516\n", "sell executed @ 17.381332397460938\n", "sell executed @ 17.016666412353516\n", "buy executed @ 16.14533233642578\n", "buy executed @ 15.38466739654541\n", "buy executed @ 14.591333389282227\n", "buy executed @ 14.66866683959961\n", "sell executed @ 14.989333152770996\n", "sell executed @ 16.19933319091797\n", "sell executed @ 16.565332412719727\n", "sell executed @ 16.604000091552734\n", "sell executed @ 15.908666610717773\n", "buy executed @ 16.512666702270508\n", "sell executed @ 16.3713321685791\n", "buy executed @ 16.1286678314209\n", "buy executed @ 16.544666290283203\n", "sell executed @ 16.5939998626709\n", "sell executed @ 16.565332412719727\n", "buy executed @ 16.682666778564453\n", "sell executed @ 16.87933349609375\n", "sell executed @ 16.904666900634766\n", "sell executed @ 17.483333587646484\n", "sell executed @ 17.471332550048828\n", "buy executed @ 17.374666213989258\n", "buy executed @ 17.613332748413086\n", "sell executed @ 17.395999908447266\n", "buy executed @ 17.40399932861328\n", "sell executed @ 17.541332244873047\n", "sell executed @ 17.12733268737793\n", "buy executed @ 16.562000274658203\n", "buy executed @ 16.44333267211914\n", "buy executed @ 16.559999465942383\n", "sell executed @ 15.991999626159668\n", "buy executed @ 16.504667282104492\n", "sell executed @ 16.40999984741211\n", "buy executed @ 16.097333908081055\n", "buy executed @ 15.46399974822998\n", "buy executed @ 15.114666938781738\n", "buy executed @ 14.712667465209961\n", "buy executed @ 14.371999740600586\n", "buy executed @ 14.616666793823242\n", "sell executed @ 14.458666801452637\n", "buy executed @ 14.753999710083008\n", "sell executed @ 15.133999824523926\n", "sell executed @ 15.206666946411133\n", "sell executed @ 14.20199966430664\n", "buy executed @ 14.005999565124512\n", "buy executed @ 14.114666938781738\n", "sell executed @ 13.939332962036133\n", "buy executed @ 14.350666999816895\n", "sell executed @ 14.023332595825195\n", "buy executed @ 14.197333335876465\n", "sell executed @ 14.108667373657227\n", "buy executed @ 13.795332908630371\n", "buy executed @ 14.252667427062988\n", "sell executed @ 13.890000343322754\n", "buy executed @ 15.442000389099121\n", "sell executed @ 15.451333045959473\n", "sell executed @ 15.490667343139648\n", "sell executed @ 15.022000312805176\n", "buy executed @ 14.433333396911621\n", "buy executed @ 14.60533332824707\n", "sell executed @ 14.196000099182129\n", "buy executed @ 13.812666893005371\n", "buy executed @ 14.287332534790039\n", "sell executed @ 14.266667366027832\n", "buy executed @ 14.73799991607666\n", "sell executed @ 14.786666870117188\n", "sell executed @ 14.667332649230957\n", "buy executed @ 14.516667366027832\n", "buy executed @ 14.550000190734863\n", "sell executed @ 15.309332847595215\n", "sell executed @ 15.440667152404785\n", "sell executed @ 15.350666999816895\n", "buy executed @ 15.812666893005371\n", "sell executed @ 15.465999603271484\n", "buy executed @ 15.513999938964844\n", "sell executed @ 15.358667373657227\n", "buy executed @ 15.408666610717773\n", "sell executed @ 15.114666938781738\n", "buy executed @ 14.968000411987305\n", "buy executed @ 15.137999534606934\n", "sell executed @ 14.468000411987305\n", "buy executed @ 14.571999549865723\n", "sell executed @ 14.739333152770996\n", "sell executed @ 15.633999824523926\n", "sell executed @ 15.559332847595215\n", "buy executed @ 15.36400032043457\n", "buy executed @ 15.503999710083008\n", "sell executed @ 15.329999923706055\n", "buy executed @ 15.313332557678223\n", "buy executed @ 15.371333122253418\n", "sell executed @ 15.263333320617676\n", "buy executed @ 15.812666893005371\n", "sell executed @ 15.87266731262207\n", "sell executed @ 16.000667572021484\n", "sell executed @ 14.894000053405762\n", "buy executed @ 14.895333290100098\n", "sell executed @ 14.602666854858398\n", "buy executed @ 14.376667022705078\n", "buy executed @ 14.066666603088379\n", "buy executed @ 13.856666564941406\n", "buy executed @ 13.998000144958496\n", "sell executed @ 13.354000091552734\n", "buy executed @ 13.745332717895508\n", "sell executed @ 13.666000366210938\n", "buy executed @ 13.64799976348877\n", "buy executed @ 13.24666690826416\n", "buy executed @ 13.33133316040039\n", "sell executed @ 13.50333309173584\n", "sell executed @ 13.092000007629395\n", "buy executed @ 12.904000282287598\n", "buy executed @ 12.538000106811523\n", "buy executed @ 12.646666526794434\n", "sell executed @ 12.74666690826416\n", "sell executed @ 13.129332542419434\n", "sell executed @ 12.185333251953125\n", "buy executed @ 11.565333366394043\n", "buy executed @ 11.688667297363281\n", "sell executed @ 10.84000015258789\n", "buy executed @ 9.866000175476074\n", "buy executed @ 9.883333206176758\n", "sell executed @ 9.57800006866455\n", "buy executed @ 10.031332969665527\n", "sell executed @ 10.06933307647705\n", "sell executed @ 10.344667434692383\n", "sell executed @ 11.245332717895508\n", "sell executed @ 11.118000030517578\n", "buy executed @ 11.10533332824707\n", "buy executed @ 11.849332809448242\n", "sell executed @ 11.814000129699707\n", "buy executed @ 11.933333396911621\n", "sell executed @ 12.495332717895508\n", "sell executed @ 12.689332962036133\n", "sell executed @ 12.795332908630371\n", "sell executed @ 12.423333168029785\n", "buy executed @ 12.555999755859375\n", "sell executed @ 13.049332618713379\n", "sell executed @ 13.402667045593262\n", "sell executed @ 13.685999870300293\n", "sell executed @ 13.506667137145996\n", "buy executed @ 13.914667129516602\n", "sell executed @ 13.678667068481445\n", "buy executed @ 13.833333015441895\n", "sell executed @ 14.34333324432373\n", "sell executed @ 14.555999755859375\n", "sell executed @ 14.795332908630371\n", "sell executed @ 15.092000007629395\n", "sell executed @ 15.515999794006348\n", "sell executed @ 15.887999534606934\n", "sell executed @ 15.616000175476074\n", "buy executed @ 14.838666915893555\n", "buy executed @ 15.183333396911621\n", "sell executed @ 15.350666999816895\n", "sell executed @ 15.342000007629395\n", "buy executed @ 15.12600040435791\n", "buy executed @ 15.317999839782715\n", "sell executed @ 15.839332580566406\n", "sell executed @ 16.465999603271484\n", "sell executed @ 17.031333923339844\n", "sell executed @ 17.69466781616211\n", "sell executed @ 17.14666748046875\n", "buy executed @ 16.67133331298828\n", "buy executed @ 16.661333084106445\n", "buy executed @ 16.521333694458008\n", "buy executed @ 16.968666076660156\n", "sell executed @ 16.790666580200195\n", "buy executed @ 16.96733283996582\n", "sell executed @ 16.92533302307129\n", "buy executed @ 16.4913330078125\n", "buy executed @ 16.6646671295166\n", "sell executed @ 16.55266761779785\n", "buy executed @ 16.916667938232422\n", "sell executed @ 16.788000106811523\n", "buy executed @ 16.916000366210938\n", "sell executed @ 16.764667510986328\n", "buy executed @ 16.513999938964844\n", "buy executed @ 16.05066680908203\n", "buy executed @ 16.1200008392334\n", "sell executed @ 15.48799991607666\n", "buy executed @ 14.837332725524902\n", "buy executed @ 14.10200023651123\n", "buy executed @ 14.328666687011719\n", "sell executed @ 13.928000450134277\n", "buy executed @ 13.912667274475098\n", "buy executed @ 13.93066692352295\n", "sell executed @ 13.8186674118042\n", "buy executed @ 13.840666770935059\n", "sell executed @ 13.88599967956543\n", "sell executed @ 13.644000053405762\n", "buy executed @ 14.07800006866455\n", "sell executed @ 14.347332954406738\n", "sell executed @ 14.685333251953125\n", "sell executed @ 14.414667129516602\n", "buy executed @ 14.52733325958252\n", "sell executed @ 14.638667106628418\n", "sell executed @ 15.008000373840332\n", "sell executed @ 14.869333267211914\n", "buy executed @ 14.881999969482422\n", "sell executed @ 14.637332916259766\n", "buy executed @ 14.597332954406738\n", "buy executed @ 14.599332809448242\n", "sell executed @ 14.711999893188477\n", "sell executed @ 15.489333152770996\n", "sell executed @ 15.701333045959473\n", "sell executed @ 15.290666580200195\n", "buy executed @ 14.586000442504883\n", "buy executed @ 14.524666786193848\n", "buy executed @ 14.330666542053223\n", "buy executed @ 14.513333320617676\n", "sell executed @ 14.528667449951172\n", "sell executed @ 14.364666938781738\n", "buy executed @ 14.646666526794434\n", "sell executed @ 14.640666961669922\n", "buy executed @ 13.11066722869873\n", "buy executed @ 13.09333324432373\n", "buy executed @ 12.876667022705078\n", "buy executed @ 13.236666679382324\n", "sell executed @ 13.452667236328125\n", "sell executed @ 14.012666702270508\n", "sell executed @ 14.152000427246094\n", "sell executed @ 14.433333396911621\n", "sell executed @ 14.26533317565918\n", "buy executed @ 14.295999526977539\n", "sell executed @ 14.395999908447266\n", "sell executed @ 14.45199966430664\n", "sell executed @ 14.985333442687988\n", "sell executed @ 14.976667404174805\n", "buy executed @ 14.835332870483398\n", "buy executed @ 14.768667221069336\n", "buy executed @ 14.69333267211914\n", "buy executed @ 15.083333015441895\n", "sell executed @ 15.017333030700684\n", "buy executed @ 15.223999977111816\n", "sell executed @ 14.699999809265137\n", "buy executed @ 14.817999839782715\n", "sell executed @ 15.333999633789062\n", "sell executed @ 15.300666809082031\n", "buy executed @ 15.232666969299316\n", "buy executed @ 15.37399959564209\n", "sell executed @ 15.652667045593262\n", "sell executed @ 15.333999633789062\n", "buy executed @ 15.146666526794434\n", "buy executed @ 15.052666664123535\n", "buy executed @ 15.37399959564209\n", "sell executed @ 15.335332870483398\n", "buy executed @ 15.077333450317383\n", "buy executed @ 15.272000312805176\n", "sell executed @ 15.043333053588867\n", "buy executed @ 14.994000434875488\n", "buy executed @ 15.040666580200195\n", "sell executed @ 15.03933334350586\n", "buy executed @ 14.907333374023438\n", "buy executed @ 14.88266658782959\n", "buy executed @ 14.900667190551758\n", "sell executed @ 15.0\n", "sell executed @ 14.862000465393066\n", "buy executed @ 14.989333152770996\n", "sell executed @ 14.841333389282227\n", "buy executed @ 14.730667114257812\n", "buy executed @ 14.666000366210938\n", "buy executed @ 14.346667289733887\n", "buy executed @ 14.089332580566406\n", "buy executed @ 14.133999824523926\n", "sell executed @ 13.38466739654541\n", "buy executed @ 13.185333251953125\n", "buy executed @ 13.522000312805176\n", "sell executed @ 13.447333335876465\n", "buy executed @ 13.157333374023438\n", "buy executed @ 12.964667320251465\n", "buy executed @ 13.220000267028809\n", "sell executed @ 13.069999694824219\n", "buy executed @ 13.093999862670898\n", "sell executed @ 13.361332893371582\n", "sell executed @ 13.69333267211914\n", "sell executed @ 13.755999565124512\n", "sell executed @ 13.642666816711426\n", "buy executed @ 13.6813325881958\n", "sell executed @ 13.76200008392334\n", "sell executed @ 13.829999923706055\n", "sell executed @ 13.932666778564453\n", "sell executed @ 13.720666885375977\n", "buy executed @ 13.751333236694336\n", "sell executed @ 13.380000114440918\n", "buy executed @ 13.60200023651123\n", "sell executed @ 14.24666690826416\n", "sell executed @ 14.093999862670898\n", "buy executed @ 13.897333145141602\n", "buy executed @ 13.399999618530273\n", "buy executed @ 13.107333183288574\n", "buy executed @ 13.396666526794434\n", "sell executed @ 13.34000015258789\n", "buy executed @ 13.434000015258789\n", "sell executed @ 13.349332809448242\n", "buy executed @ 13.100666999816895\n", "buy executed @ 12.93066692352295\n", "buy executed @ 13.273332595825195\n", "sell executed @ 13.570667266845703\n", "sell executed @ 13.273332595825195\n", "buy executed @ 13.339332580566406\n", "sell executed @ 13.517333030700684\n", "sell executed @ 13.489333152770996\n", "buy executed @ 13.482666969299316\n", "buy executed @ 13.600666999816895\n", "sell executed @ 13.33133316040039\n", "buy executed @ 13.182000160217285\n", "buy executed @ 12.719332695007324\n", "buy executed @ 12.534667015075684\n", "buy executed @ 12.494667053222656\n", "buy executed @ 12.704000473022461\n", "sell executed @ 12.880666732788086\n", "sell executed @ 12.996000289916992\n", "sell executed @ 12.670666694641113\n", "buy executed @ 12.356666564941406\n", "buy executed @ 12.570667266845703\n", "sell executed @ 12.096667289733887\n", "buy executed @ 12.251333236694336\n", "sell executed @ 12.26200008392334\n", "sell executed @ 12.577333450317383\n", "sell executed @ 12.334667205810547\n", "buy executed @ 12.3013334274292\n", "buy executed @ 12.744667053222656\n", "sell executed @ 12.87600040435791\n", "sell executed @ 13.109999656677246\n", "sell executed @ 13.074666976928711\n", "buy executed @ 12.637999534606934\n", "buy executed @ 12.626667022705078\n", "buy executed @ 12.125332832336426\n", "buy executed @ 12.097999572753906\n", "buy executed @ 12.453332901000977\n", "sell executed @ 12.390000343322754\n", "buy executed @ 12.876667022705078\n", "sell executed @ 12.81933307647705\n", "buy executed @ 12.812000274658203\n", "buy executed @ 12.828666687011719\n", "sell executed @ 13.210000038146973\n", "sell executed @ 13.246000289916992\n", "sell executed @ 13.17199993133545\n", "buy executed @ 13.499333381652832\n", "sell executed @ 13.51533317565918\n", "sell executed @ 13.919333457946777\n", "sell executed @ 13.846667289733887\n", "buy executed @ 13.896666526794434\n", "sell executed @ 14.22266674041748\n", "sell executed @ 14.635333061218262\n", "sell executed @ 14.649333000183105\n", "sell executed @ 14.312000274658203\n", "buy executed @ 14.246000289916992\n", "buy executed @ 14.465999603271484\n", "sell executed @ 15.13266658782959\n", "sell executed @ 15.116666793823242\n", "buy executed @ 15.267333030700684\n", "sell executed @ 15.41866683959961\n", "sell executed @ 15.324666976928711\n", "buy executed @ 15.315333366394043\n", "buy executed @ 15.305999755859375\n", "buy executed @ 15.850000381469727\n", "sell executed @ 15.70533275604248\n", "buy executed @ 15.890666961669922\n", "sell executed @ 16.250667572021484\n", "sell executed @ 16.315332412719727\n", "sell executed @ 16.594667434692383\n", "sell executed @ 16.974000930786133\n", "sell executed @ 16.96466636657715\n", "buy executed @ 16.833999633789062\n", "buy executed @ 16.863332748413086\n", "sell executed @ 16.708667755126953\n", "buy executed @ 16.795333862304688\n", "sell executed @ 16.615999221801758\n", "buy executed @ 16.770000457763672\n", "sell executed @ 16.755332946777344\n", "buy executed @ 17.184667587280273\n", "sell executed @ 17.165332794189453\n", "buy executed @ 17.472000122070312\n", "sell executed @ 17.946666717529297\n", "sell executed @ 17.948667526245117\n", "sell executed @ 18.706666946411133\n", "sell executed @ 18.73200035095215\n", "sell executed @ 18.650667190551758\n", "buy executed @ 17.93000030517578\n", "buy executed @ 18.148666381835938\n", "sell executed @ 18.492666244506836\n", "sell executed @ 18.233999252319336\n", "buy executed @ 17.06599998474121\n", "buy executed @ 17.133333206176758\n", "sell executed @ 16.415332794189453\n", "buy executed @ 16.666000366210938\n", "sell executed @ 16.667999267578125\n", "sell executed @ 16.698667526245117\n", "sell executed @ 16.771333694458008\n", "sell executed @ 16.747333526611328\n", "buy executed @ 16.57266616821289\n", "buy executed @ 16.45800018310547\n", "buy executed @ 16.32666778564453\n", "buy executed @ 16.246000289916992\n", "buy executed @ 16.411333084106445\n", "sell executed @ 17.200000762939453\n", "sell executed @ 17.048667907714844\n", "buy executed @ 17.469999313354492\n", "sell executed @ 17.433332443237305\n", "buy executed @ 17.461332321166992\n", "sell executed @ 16.711999893188477\n", "buy executed @ 17.000667572021484\n", "sell executed @ 16.985332489013672\n", "buy executed @ 17.54400062561035\n", "sell executed @ 18.014667510986328\n", "sell executed @ 18.496667861938477\n", "sell executed @ 18.492000579833984\n", "buy executed @ 18.527999877929688\n", "sell executed @ 18.553333282470703\n", "sell executed @ 19.90133285522461\n", "sell executed @ 20.246667861938477\n", "sell executed @ 19.666667938232422\n", "buy executed @ 19.913333892822266\n", "sell executed @ 20.16933250427246\n", "sell executed @ 20.826000213623047\n", "sell executed @ 20.58066749572754\n", "buy executed @ 19.78933334350586\n", "buy executed @ 20.266666412353516\n", "sell executed @ 20.09600067138672\n", "buy executed @ 20.016666412353516\n", "buy executed @ 20.368000030517578\n", "sell executed @ 20.167333602905273\n", "buy executed @ 20.373332977294922\n", "sell executed @ 20.53533363342285\n", "sell executed @ 20.91933250427246\n", "sell executed @ 20.67799949645996\n", "buy executed @ 20.575332641601562\n", "buy executed @ 20.937999725341797\n", "sell executed @ 21.52199935913086\n", "sell executed @ 21.25933265686035\n", "buy executed @ 20.73466682434082\n", "buy executed @ 19.69733238220215\n", "buy executed @ 20.55666732788086\n", "sell executed @ 20.479333877563477\n", "buy executed @ 21.417333602905273\n", "sell executed @ 21.681333541870117\n", "sell executed @ 21.540000915527344\n", "buy executed @ 21.65399932861328\n", "sell executed @ 21.058666229248047\n", "buy executed @ 21.134000778198242\n", "sell executed @ 20.407333374023438\n", "buy executed @ 20.87066650390625\n", "sell executed @ 20.722000122070312\n", "buy executed @ 20.690000534057617\n", "buy executed @ 20.257333755493164\n", "buy executed @ 20.681333541870117\n", "sell executed @ 21.121999740600586\n", "sell executed @ 21.676000595092773\n", "sell executed @ 22.34000015258789\n", "sell executed @ 22.733999252319336\n", "sell executed @ 22.691333770751953\n", "buy executed @ 22.656667709350586\n", "buy executed @ 23.154666900634766\n", "sell executed @ 23.523332595825195\n", "sell executed @ 23.976667404174805\n", "sell executed @ 24.666667938232422\n", "sell executed @ 23.821332931518555\n", "buy executed @ 23.93400001525879\n", "sell executed @ 25.06333351135254\n", "sell executed @ 25.37733268737793\n", "sell executed @ 25.022666931152344\n", "buy executed @ 24.760000228881836\n", "buy executed @ 24.65333366394043\n", "buy executed @ 24.81599998474121\n", "sell executed @ 25.093332290649414\n", "sell executed @ 25.507333755493164\n", "sell executed @ 25.56333351135254\n", "sell executed @ 25.166000366210938\n", "buy executed @ 24.158000946044922\n", "buy executed @ 24.749332427978516\n", "sell executed @ 24.049999237060547\n", "buy executed @ 24.107332229614258\n", "sell executed @ 23.507999420166016\n", "buy executed @ 21.805999755859375\n", "buy executed @ 20.588666915893555\n", "buy executed @ 20.881332397460938\n", "sell executed @ 21.06999969482422\n", "sell executed @ 21.814666748046875\n", "sell executed @ 21.968000411987305\n", "sell executed @ 21.560667037963867\n", "buy executed @ 21.851999282836914\n", "sell executed @ 21.30466651916504\n", "buy executed @ 21.882667541503906\n", "sell executed @ 21.68400001525879\n", "buy executed @ 21.994667053222656\n", "sell executed @ 21.893333435058594\n", "buy executed @ 22.834667205810547\n", "sell executed @ 22.639999389648438\n", "buy executed @ 22.92333221435547\n", "sell executed @ 22.297332763671875\n", "buy executed @ 22.33799934387207\n", "sell executed @ 21.564666748046875\n", "buy executed @ 21.30466651916504\n", "buy executed @ 21.72599983215332\n", "sell executed @ 23.139333724975586\n", "sell executed @ 23.79400062561035\n", "sell executed @ 23.67799949645996\n", "buy executed @ 24.347999572753906\n", "sell executed @ 24.235332489013672\n", "buy executed @ 23.69333267211914\n", "buy executed @ 23.857999801635742\n", "sell executed @ 24.253332138061523\n", "sell executed @ 24.155332565307617\n", "buy executed @ 24.194000244140625\n", "sell executed @ 23.461332321166992\n", "buy executed @ 23.163999557495117\n", "buy executed @ 22.52400016784668\n", "buy executed @ 22.75666618347168\n", "sell executed @ 23.51799964904785\n", "sell executed @ 23.528667449951172\n", "sell executed @ 23.203332901000977\n", "buy executed @ 23.04400062561035\n", "buy executed @ 23.157333374023438\n", "sell executed @ 23.545333862304688\n", "sell executed @ 23.726667404174805\n", "sell executed @ 23.69333267211914\n", "buy executed @ 23.305999755859375\n", "buy executed @ 22.968666076660156\n", "buy executed @ 23.374000549316406\n", "sell executed @ 22.893333435058594\n", "buy executed @ 24.246000289916992\n", "sell executed @ 24.183332443237305\n", "buy executed @ 24.415332794189453\n", "sell executed @ 25.176000595092773\n", "sell executed @ 25.320667266845703\n", "sell executed @ 25.666667938232422\n", "sell executed @ 25.00666618347168\n", "buy executed @ 24.92733383178711\n", "buy executed @ 24.43199920654297\n", "buy executed @ 23.4060001373291\n", "buy executed @ 22.999332427978516\n", "buy executed @ 23.016666412353516\n", "sell executed @ 22.731332778930664\n", "buy executed @ 22.639999389648438\n", "buy executed @ 22.739999771118164\n", "sell executed @ 22.768667221069336\n", "sell executed @ 23.209333419799805\n", "sell executed @ 23.667333602905273\n", "sell executed @ 23.68866729736328\n", "sell executed @ 23.79199981689453\n", "sell executed @ 22.862667083740234\n", "buy executed @ 23.70599937438965\n", "sell executed @ 23.639999389648438\n", "buy executed @ 23.711999893188477\n", "sell executed @ 23.704666137695312\n", "buy executed @ 23.373332977294922\n", "buy executed @ 23.71666717529297\n", "sell executed @ 23.976667404174805\n", "sell executed @ 23.45400047302246\n", "buy executed @ 23.00666618347168\n", "buy executed @ 22.468000411987305\n", "buy executed @ 22.48933219909668\n", "sell executed @ 21.722667694091797\n", "buy executed @ 21.744667053222656\n", "sell executed @ 21.391332626342773\n", "buy executed @ 21.338666915893555\n", "buy executed @ 22.101999282836914\n", "sell executed @ 21.405332565307617\n", "buy executed @ 19.950666427612305\n", "buy executed @ 20.4060001373291\n", "sell executed @ 20.185333251953125\n", "buy executed @ 20.40333366394043\n", "sell executed @ 20.292667388916016\n", "buy executed @ 20.19933319091797\n", "buy executed @ 20.19933319091797\n", "sell executed @ 20.579999923706055\n", "buy executed @ 20.753332138061523\n", "sell executed @ 20.833332061767578\n", "sell executed @ 21.003332138061523\n", "sell executed @ 20.582666397094727\n", "buy executed @ 21.187332153320312\n", "sell executed @ 20.84000015258789\n", "buy executed @ 21.036666870117188\n", "sell executed @ 21.12066650390625\n", "sell executed @ 21.170000076293945\n", "sell executed @ 20.502666473388672\n", "buy executed @ 20.59000015258789\n", "sell executed @ 20.435333251953125\n", "buy executed @ 20.34666633605957\n", "buy executed @ 20.246667861938477\n", "buy executed @ 20.884000778198242\n", "sell executed @ 20.749332427978516\n", "buy executed @ 21.0086669921875\n", "sell executed @ 21.92733383178711\n", "sell executed @ 22.735332489013672\n", "sell executed @ 22.601999282836914\n", "buy executed @ 22.525999069213867\n", "buy executed @ 22.89666748046875\n", "sell executed @ 22.591333389282227\n", "buy executed @ 22.073333740234375\n", "buy executed @ 21.93199920654297\n", "buy executed @ 22.110666275024414\n", "sell executed @ 21.68000030517578\n", "buy executed @ 21.152666091918945\n", "buy executed @ 20.775999069213867\n", "buy executed @ 21.02400016784668\n", "sell executed @ 20.75666618347168\n", "buy executed @ 21.368667602539062\n", "sell executed @ 21.149999618530273\n", "buy executed @ 20.974666595458984\n", "buy executed @ 21.10533332824707\n", "sell executed @ 22.42733383178711\n", "sell executed @ 22.246000289916992\n", "buy executed @ 22.31999969482422\n", "sell executed @ 22.530000686645508\n", "sell executed @ 22.4146671295166\n", "buy executed @ 22.67066764831543\n", "sell executed @ 23.143999099731445\n", "sell executed @ 22.971332550048828\n", "buy executed @ 23.334667205810547\n", "sell executed @ 23.437332153320312\n", "sell executed @ 23.519332885742188\n", "sell executed @ 23.05933380126953\n", "buy executed @ 22.50933265686035\n", "buy executed @ 22.856666564941406\n", "sell executed @ 23.302000045776367\n", "sell executed @ 23.05466651916504\n", "buy executed @ 23.62066650390625\n", "sell executed @ 23.28333282470703\n", "buy executed @ 22.916667938232422\n", "buy executed @ 22.208667755126953\n", "buy executed @ 22.264667510986328\n", "sell executed @ 23.0\n", "sell executed @ 21.01533317565918\n", "buy executed @ 20.69466781616211\n", "buy executed @ 21.048667907714844\n", "sell executed @ 21.577333450317383\n", "sell executed @ 21.487333297729492\n", "buy executed @ 22.271333694458008\n", "sell executed @ 22.365999221801758\n", "sell executed @ 22.31800079345703\n", "buy executed @ 22.219999313354492\n", "buy executed @ 23.077999114990234\n", "sell executed @ 23.469999313354492\n", "sell executed @ 23.827999114990234\n", "sell executed @ 23.39933204650879\n", "buy executed @ 22.87066650390625\n", "buy executed @ 22.062000274658203\n", "buy executed @ 22.341333389282227\n", "sell executed @ 22.22333335876465\n", "buy executed @ 21.8799991607666\n", "buy executed @ 22.15333366394043\n", "sell executed @ 21.940000534057617\n", "buy executed @ 21.81133270263672\n", "buy executed @ 23.034000396728516\n", "sell executed @ 22.78933334350586\n", "buy executed @ 21.775333404541016\n", "buy executed @ 21.706666946411133\n", "buy executed @ 21.42333221435547\n", "buy executed @ 20.90399932861328\n", "buy executed @ 20.703332901000977\n", "buy executed @ 21.101999282836914\n", "sell executed @ 20.606666564941406\n", "buy executed @ 20.1026668548584\n", "buy executed @ 20.278667449951172\n", "sell executed @ 18.61199951171875\n", "buy executed @ 17.185333251953125\n", "buy executed @ 17.742000579833984\n", "sell executed @ 16.832000732421875\n", "buy executed @ 17.8353328704834\n", "sell executed @ 19.12933349609375\n", "sell executed @ 20.381332397460938\n", "sell executed @ 19.953332901000977\n", "buy executed @ 19.310667037963867\n", "buy executed @ 20.31333351135254\n", "sell executed @ 20.062000274658203\n", "buy executed @ 19.60533332824707\n", "buy executed @ 20.022666931152344\n", "sell executed @ 19.413999557495117\n", "buy executed @ 19.179332733154297\n", "buy executed @ 19.55666732788086\n", "sell executed @ 20.005332946777344\n", "sell executed @ 19.349332809448242\n", "buy executed @ 18.891332626342773\n", "buy executed @ 18.8973331451416\n", "sell executed @ 18.71266746520996\n", "buy executed @ 19.031999588012695\n", "sell executed @ 19.60533332824707\n", "sell executed @ 19.593332290649414\n", "buy executed @ 19.994667053222656\n", "sell executed @ 20.07666778564453\n", "sell executed @ 18.963333129882812\n", "buy executed @ 19.606000900268555\n", "sell executed @ 20.184667587280273\n", "sell executed @ 20.131332397460938\n", "buy executed @ 20.456666946411133\n", "sell executed @ 20.334667205810547\n", "buy executed @ 20.070667266845703\n", "buy executed @ 19.46466636657715\n", "buy executed @ 18.94533348083496\n", "buy executed @ 19.09866714477539\n", "sell executed @ 18.96933364868164\n", "buy executed @ 18.454666137695312\n", "buy executed @ 18.965999603271484\n", "sell executed @ 18.333999633789062\n", "buy executed @ 18.60466766357422\n", "sell executed @ 18.523332595825195\n", "buy executed @ 18.59000015258789\n", "sell executed @ 18.917333602905273\n", "sell executed @ 19.447999954223633\n", "sell executed @ 18.98200035095215\n", "buy executed @ 19.454666137695312\n", "sell executed @ 19.78266716003418\n", "sell executed @ 19.408666610717773\n", "buy executed @ 21.299999237060547\n", "sell executed @ 21.07266616821289\n", "buy executed @ 21.17733383178711\n", "sell executed @ 22.139999389648438\n", "sell executed @ 22.851333618164062\n", "sell executed @ 22.985332489013672\n", "sell executed @ 23.847999572753906\n", "sell executed @ 23.878000259399414\n", "sell executed @ 24.722000122070312\n", "sell executed @ 23.503332138061523\n", "buy executed @ 24.148000717163086\n", "sell executed @ 23.167333602905273\n", "buy executed @ 22.242000579833984\n", "buy executed @ 22.200666427612305\n", "buy executed @ 22.799999237060547\n", "sell executed @ 22.96666717529297\n", "sell executed @ 23.32866668701172\n", "sell executed @ 22.863332748413086\n", "buy executed @ 22.33799934387207\n", "buy executed @ 20.724000930786133\n", "buy executed @ 20.610666275024414\n", "buy executed @ 20.593332290649414\n", "buy executed @ 21.233999252319336\n", "sell executed @ 21.49799919128418\n", "sell executed @ 21.263999938964844\n", "buy executed @ 21.11400032043457\n", "buy executed @ 21.257999420166016\n", "sell executed @ 20.67333221435547\n", "buy executed @ 21.512666702270508\n", "sell executed @ 21.59000015258789\n", "sell executed @ 21.34866714477539\n", "buy executed @ 20.905332565307617\n", "buy executed @ 20.213333129882812\n", "buy executed @ 19.82866668701172\n", "buy executed @ 20.582666397094727\n", "sell executed @ 20.44333267211914\n", "buy executed @ 19.812000274658203\n", "buy executed @ 19.344667434692383\n", "buy executed @ 19.875999450683594\n", "sell executed @ 20.055999755859375\n", "sell executed @ 23.30266761779785\n", "sell executed @ 23.211332321166992\n", "buy executed @ 22.799333572387695\n", "buy executed @ 25.30466651916504\n", "sell executed @ 24.689332962036133\n", "buy executed @ 23.496667861938477\n", "buy executed @ 23.69933319091797\n", "sell executed @ 23.76066780090332\n", "sell executed @ 23.176000595092773\n", "buy executed @ 22.57933235168457\n", "buy executed @ 22.363332748413086\n", "buy executed @ 20.366666793823242\n", "buy executed @ 20.562667846679688\n", "sell executed @ 21.459999084472656\n", "sell executed @ 21.44266700744629\n", "buy executed @ 21.34000015258789\n", "buy executed @ 21.521333694458008\n", "sell executed @ 21.284666061401367\n", "buy executed @ 20.790666580200195\n", "buy executed @ 20.333999633789062\n", "buy executed @ 20.209999084472656\n", "buy executed @ 20.110666275024414\n", "buy executed @ 19.26333236694336\n", "buy executed @ 18.715999603271484\n", "buy executed @ 18.729999542236328\n", "sell executed @ 17.549333572387695\n", "buy executed @ 19.03333282470703\n", "sell executed @ 18.62933349609375\n", "buy executed @ 19.369333267211914\n", "sell executed @ 19.297332763671875\n", "buy executed @ 19.68000030517578\n", "sell executed @ 19.6560001373291\n", "buy executed @ 18.997333526611328\n", "buy executed @ 19.934667587280273\n", "sell executed @ 19.8886661529541\n", "buy executed @ 19.940000534057617\n", "sell executed @ 19.978666305541992\n", "sell executed @ 20.06599998474121\n", "sell executed @ 20.6386661529541\n", "sell executed @ 20.501333236694336\n", "buy executed @ 17.65133285522461\n", "buy executed @ 20.713333129882812\n", "sell executed @ 20.06800079345703\n", "buy executed @ 19.65333366394043\n", "buy executed @ 18.788667678833008\n", "buy executed @ 17.463333129882812\n", "buy executed @ 16.70400047302246\n", "buy executed @ 17.520000457763672\n", "sell executed @ 17.125333786010742\n", "buy executed @ 16.815332412719727\n", "buy executed @ 17.25200080871582\n", "sell executed @ 17.305999755859375\n", "sell executed @ 18.439332962036133\n", "sell executed @ 18.118667602539062\n", "buy executed @ 17.5939998626709\n", "buy executed @ 17.333332061767578\n", "buy executed @ 17.39666748046875\n", "sell executed @ 19.609333038330078\n", "sell executed @ 19.233333587646484\n", "buy executed @ 20.99066734313965\n", "sell executed @ 22.059999465942383\n", "sell executed @ 22.323333740234375\n", "sell executed @ 21.99333381652832\n", "buy executed @ 22.488000869750977\n", "sell executed @ 22.95199966430664\n", "sell executed @ 23.0939998626709\n", "sell executed @ 22.760000228881836\n", "buy executed @ 22.737333297729492\n", "buy executed @ 23.21066665649414\n", "sell executed @ 23.426666259765625\n", "sell executed @ 23.367332458496094\n", "buy executed @ 22.0853328704834\n", "buy executed @ 22.582000732421875\n", "sell executed @ 22.933332443237305\n", "sell executed @ 23.229333877563477\n", "sell executed @ 23.62066650390625\n", "sell executed @ 23.564666748046875\n", "buy executed @ 23.166000366210938\n", "buy executed @ 22.54599952697754\n", "buy executed @ 21.722000122070312\n", "buy executed @ 23.066667556762695\n", "sell executed @ 22.92799949645996\n", "buy executed @ 23.191333770751953\n", "sell executed @ 22.744667053222656\n", "buy executed @ 23.365333557128906\n", "sell executed @ 23.89933204650879\n", "sell executed @ 23.979999542236328\n", "sell executed @ 24.20400047302246\n", "sell executed @ 23.864667892456055\n", "buy executed @ 24.343332290649414\n", "sell executed @ 24.450666427612305\n", "sell executed @ 24.440000534057617\n", "buy executed @ 25.119333267211914\n", "sell executed @ 24.380666732788086\n", "buy executed @ 23.22800064086914\n", "buy executed @ 22.468666076660156\n", "buy executed @ 22.197999954223633\n", "buy executed @ 21.025333404541016\n", "buy executed @ 21.31800079345703\n", "sell executed @ 19.69266700744629\n", "buy executed @ 21.73933219909668\n", "sell executed @ 21.075332641601562\n", "buy executed @ 22.257999420166016\n", "sell executed @ 22.18666648864746\n", "buy executed @ 20.674667358398438\n", "buy executed @ 20.02400016784668\n", "buy executed @ 21.179332733154297\n", "sell executed @ 22.33066749572754\n", "sell executed @ 22.356666564941406\n", "sell executed @ 22.568666458129883\n", "sell executed @ 22.99799919128418\n", "sell executed @ 23.150667190551758\n", "sell executed @ 22.293333053588867\n", "buy executed @ 22.961999893188477\n", "sell executed @ 23.06999969482422\n", "sell executed @ 23.15399932861328\n", "sell executed @ 20.150667190551758\n", "buy executed @ 19.92799949645996\n", "buy executed @ 19.172666549682617\n", "buy executed @ 19.43400001525879\n", "sell executed @ 19.80266761779785\n", "sell executed @ 19.7586669921875\n", "buy executed @ 19.83066749572754\n", "sell executed @ 20.584667205810547\n", "sell executed @ 20.468000411987305\n", "buy executed @ 20.81399917602539\n", "sell executed @ 20.859333038330078\n", "sell executed @ 21.42333221435547\n", "sell executed @ 21.148000717163086\n", "buy executed @ 20.500667572021484\n", "buy executed @ 20.386667251586914\n", "buy executed @ 20.856000900268555\n", "sell executed @ 20.78733253479004\n", "buy executed @ 20.544666290283203\n", "buy executed @ 20.251333236694336\n", "buy executed @ 20.525333404541016\n", "sell executed @ 20.375999450683594\n", "buy executed @ 20.17066764831543\n", "buy executed @ 19.415332794189453\n", "buy executed @ 19.6473331451416\n", "sell executed @ 19.917999267578125\n", "sell executed @ 19.857332229614258\n", "buy executed @ 20.982667922973633\n", "sell executed @ 21.325332641601562\n", "sell executed @ 19.652666091918945\n", "buy executed @ 19.02400016784668\n", "buy executed @ 18.43600082397461\n", "buy executed @ 18.416000366210938\n", "buy executed @ 18.439332962036133\n", "sell executed @ 18.94266700744629\n", "sell executed @ 19.39466667175293\n", "sell executed @ 18.890666961669922\n", "buy executed @ 19.263999938964844\n", "sell executed @ 19.33066749572754\n", "sell executed @ 18.36199951171875\n", "buy executed @ 17.965999603271484\n", "buy executed @ 17.83133316040039\n", "buy executed @ 18.239999771118164\n", "sell executed @ 18.26799964904785\n", "sell executed @ 17.635332107543945\n", "buy executed @ 17.3613338470459\n", "buy executed @ 17.851333618164062\n", "sell executed @ 18.32200050354004\n", "sell executed @ 18.57466697692871\n", "sell executed @ 18.657333374023438\n", "sell executed @ 19.278667449951172\n", "sell executed @ 19.058666229248047\n", "buy executed @ 19.45400047302246\n", "sell executed @ 17.851999282836914\n", "buy executed @ 18.33066749572754\n", "sell executed @ 18.213333129882812\n", "buy executed @ 18.15399932861328\n", "buy executed @ 18.40399932861328\n", "sell executed @ 17.89466667175293\n", "buy executed @ 17.84666633605957\n", "buy executed @ 17.7586669921875\n", "buy executed @ 18.224000930786133\n", "sell executed @ 18.082000732421875\n", "buy executed @ 18.21733283996582\n", "sell executed @ 17.516666412353516\n", "buy executed @ 17.593332290649414\n", "sell executed @ 17.243999481201172\n", "buy executed @ 16.5086669921875\n", "buy executed @ 15.675999641418457\n", "buy executed @ 16.097999572753906\n", "sell executed @ 15.912667274475098\n", "buy executed @ 15.600666999816895\n", "buy executed @ 16.273332595825195\n", "sell executed @ 17.00200080871582\n", "sell executed @ 17.022666931152344\n", "sell executed @ 16.470666885375977\n", "buy executed @ 16.32266616821289\n", "buy executed @ 16.131999969482422\n", "buy executed @ 15.968000411987305\n", "buy executed @ 15.133999824523926\n", "buy executed @ 15.487333297729492\n", "sell executed @ 15.463333129882812\n", "buy executed @ 15.222000122070312\n", "buy executed @ 14.0686674118042\n", "buy executed @ 13.690667152404785\n", "buy executed @ 13.67199993133545\n", "buy executed @ 12.84866714477539\n", "buy executed @ 13.03266716003418\n", "sell executed @ 12.708666801452637\n", "buy executed @ 12.579999923706055\n", "buy executed @ 12.657333374023438\n", "sell executed @ 12.54800033569336\n", "buy executed @ 12.343999862670898\n", "buy executed @ 11.9313325881958\n", "buy executed @ 12.90666675567627\n", "sell executed @ 13.105999946594238\n", "sell executed @ 13.729999542236328\n", "sell executed @ 13.633333206176758\n", "buy executed @ 14.192000389099121\n", "sell executed @ 14.473333358764648\n", "sell executed @ 13.950667381286621\n", "buy executed @ 14.260666847229004\n", "sell executed @ 14.32800006866455\n", "sell executed @ 15.001999855041504\n", "sell executed @ 14.982666969299316\n", "buy executed @ 15.095333099365234\n", "sell executed @ 14.641332626342773\n", "buy executed @ 14.790666580200195\n", "sell executed @ 14.909333229064941\n", "sell executed @ 14.650667190551758\n", "buy executed @ 14.618000030517578\n", "buy executed @ 14.855999946594238\n", "sell executed @ 14.897333145141602\n", "sell executed @ 15.14466667175293\n", "sell executed @ 14.970000267028809\n", "buy executed @ 15.65999984741211\n", "sell executed @ 15.539999961853027\n", "buy executed @ 15.355999946594238\n", "buy executed @ 15.337332725524902\n", "buy executed @ 15.928000450134277\n", "sell executed @ 15.90666675567627\n", "buy executed @ 16.338666915893555\n", "sell executed @ 16.899999618530273\n", "sell executed @ 16.825332641601562\n", "buy executed @ 16.99066734313965\n", "sell executed @ 16.902666091918945\n", "buy executed @ 17.211999893188477\n", "sell executed @ 17.045333862304688\n", "buy executed @ 17.344667434692383\n", "sell executed @ 17.658666610717773\n", "sell executed @ 15.254667282104492\n", "buy executed @ 15.202667236328125\n", "buy executed @ 15.718000411987305\n", "sell executed @ 16.150667190551758\n", "sell executed @ 16.107332229614258\n", "buy executed @ 15.59000015258789\n", "buy executed @ 15.62266731262207\n", "sell executed @ 15.221332550048828\n", "buy executed @ 15.383333206176758\n", "sell executed @ 15.561332702636719\n", "sell executed @ 15.886667251586914\n", "sell executed @ 15.667332649230957\n", "buy executed @ 15.267333030700684\n", "buy executed @ 15.666666984558105\n", "sell executed @ 14.641332626342773\n", "buy executed @ 14.37600040435791\n", "buy executed @ 14.662667274475098\n", "sell executed @ 15.121999740600586\n", "sell executed @ 15.057332992553711\n", "buy executed @ 14.722000122070312\n", "buy executed @ 14.8100004196167\n", "sell executed @ 14.09333324432373\n", "buy executed @ 14.333333015441895\n", "sell executed @ 14.272000312805176\n", "buy executed @ 14.37266731262207\n", "sell executed @ 14.780667304992676\n", "sell executed @ 15.040666580200195\n", "sell executed @ 15.000666618347168\n", "buy executed @ 14.711999893188477\n", "buy executed @ 15.305333137512207\n", "sell executed @ 15.16333293914795\n", "buy executed @ 15.452667236328125\n", "sell executed @ 15.702667236328125\n", "sell executed @ 16.47333335876465\n", "sell executed @ 16.391332626342773\n", "buy executed @ 16.34666633605957\n", "buy executed @ 16.187332153320312\n", "buy executed @ 16.319332122802734\n", "sell executed @ 16.232667922973633\n", "buy executed @ 16.440000534057617\n", "sell executed @ 16.041332244873047\n", "buy executed @ 16.082000732421875\n", "sell executed @ 14.880666732788086\n", "buy executed @ 15.24666690826416\n", "sell executed @ 16.17066764831543\n", "sell executed @ 16.142000198364258\n", "buy executed @ 16.058000564575195\n", "buy executed @ 16.312667846679688\n", "sell executed @ 16.208667755126953\n", "buy executed @ 15.535332679748535\n", "buy executed @ 15.428667068481445\n", "buy executed @ 15.847999572753906\n", "sell executed @ 16.003332138061523\n", "sell executed @ 16.302000045776367\n", "sell executed @ 16.31599998474121\n", "sell executed @ 16.525999069213867\n", "sell executed @ 17.130666732788086\n", "sell executed @ 17.19266700744629\n", "sell executed @ 17.316667556762695\n", "sell executed @ 17.46466636657715\n", "sell executed @ 17.1299991607666\n", "buy executed @ 16.899999618530273\n", "buy executed @ 17.038667678833008\n", "sell executed @ 16.978666305541992\n", "buy executed @ 19.978666305541992\n", "sell executed @ 21.875333786010742\n", "sell executed @ 21.847333908081055\n", "buy executed @ 21.08133316040039\n", "buy executed @ 21.000667572021484\n", "buy executed @ 20.994667053222656\n", "buy executed @ 20.887332916259766\n", "buy executed @ 21.1646671295166\n", "sell executed @ 21.148000717163086\n", "buy executed @ 21.77199935913086\n", "sell executed @ 22.369333267211914\n", "sell executed @ 22.47599983215332\n", "sell executed @ 23.006000518798828\n", "sell executed @ 23.32866668701172\n", "sell executed @ 23.073999404907227\n", "buy executed @ 23.290000915527344\n", "sell executed @ 23.47800064086914\n", "sell executed @ 23.332666397094727\n", "buy executed @ 23.968000411987305\n", "sell executed @ 23.481332778930664\n", "buy executed @ 23.655332565307617\n", "sell executed @ 22.202667236328125\n", "buy executed @ 22.422666549682617\n", "sell executed @ 21.92799949645996\n", "buy executed @ 22.086000442504883\n", "sell executed @ 21.996000289916992\n", "buy executed @ 22.32466697692871\n", "sell executed @ 22.413333892822266\n", "sell executed @ 22.20199966430664\n", "buy executed @ 22.024667739868164\n", "buy executed @ 22.392667770385742\n", "sell executed @ 22.635332107543945\n", "sell executed @ 23.256000518798828\n", "sell executed @ 23.51333236694336\n", "sell executed @ 23.978666305541992\n", "sell executed @ 23.892667770385742\n", "buy executed @ 25.433332443237305\n", "sell executed @ 25.266000747680664\n", "buy executed @ 26.209999084472656\n", "sell executed @ 26.93600082397461\n", "sell executed @ 27.03933334350586\n", "sell executed @ 27.947999954223633\n", "sell executed @ 28.350000381469727\n", "sell executed @ 28.729333877563477\n", "sell executed @ 28.691999435424805\n", "buy executed @ 27.64666748046875\n", "buy executed @ 27.8886661529541\n", "sell executed @ 28.68400001525879\n", "sell executed @ 29.534000396728516\n", "sell executed @ 30.1026668548584\n", "sell executed @ 31.270666122436523\n", "sell executed @ 32.80933380126953\n", "sell executed @ 32.089332580566406\n", "buy executed @ 31.876667022705078\n", "buy executed @ 34.990665435791016\n", "sell executed @ 35.861331939697266\n", "sell executed @ 34.56666564941406\n", "buy executed @ 34.232666015625\n", "buy executed @ 34.03333282470703\n", "buy executed @ 36.47999954223633\n", "sell executed @ 37.97066879272461\n", "sell executed @ 38.14666748046875\n", "sell executed @ 37.654666900634766\n", "buy executed @ 37.201332092285156\n", "buy executed @ 37.793331146240234\n", "sell executed @ 38.732666015625\n", "sell executed @ 42.72066879272461\n", "sell executed @ 43.371334075927734\n", "sell executed @ 52.0\n", "sell executed @ 59.137332916259766\n", "sell executed @ 48.97999954223633\n", "buy executed @ 49.930667877197266\n", "sell executed @ 49.871334075927734\n", "buy executed @ 51.41866683959961\n", "sell executed @ 51.62533187866211\n", "sell executed @ 51.15266799926758\n", "buy executed @ 53.599998474121094\n", "sell executed @ 53.33533477783203\n", "buy executed @ 57.22666549682617\n", "sell executed @ 61.16133117675781\n", "sell executed @ 59.96066665649414\n", "buy executed @ 60.06666564941406\n", "sell executed @ 55.58599853515625\n", "buy executed @ 53.32733154296875\n", "buy executed @ 51.91999816894531\n", "buy executed @ 45.266666412353516\n", "buy executed @ 44.53266525268555\n", "buy executed @ 49.574668884277344\n", "sell executed @ 49.70066833496094\n", "sell executed @ 49.96666717529297\n", "sell executed @ 48.30266571044922\n", "buy executed @ 46.89866638183594\n", "buy executed @ 40.53333282470703\n", "buy executed @ 43.02199935913086\n", "sell executed @ 42.28200149536133\n", "buy executed @ 37.369998931884766\n", "buy executed @ 36.44133377075195\n", "buy executed @ 29.67133331298828\n", "buy executed @ 28.68000030517578\n", "buy executed @ 24.08133316040039\n", "buy executed @ 28.50933265686035\n", "sell executed @ 28.50200080871582\n", "buy executed @ 28.952667236328125\n", "sell executed @ 33.66666793823242\n", "sell executed @ 35.95000076293945\n", "sell executed @ 35.21066665649414\n", "buy executed @ 34.29066848754883\n", "buy executed @ 33.47533416748047\n", "buy executed @ 34.93333435058594\n", "sell executed @ 32.104000091552734\n", "buy executed @ 30.29800033569336\n", "buy executed @ 32.000667572021484\n", "sell executed @ 34.41600036621094\n", "sell executed @ 36.36333465576172\n", "sell executed @ 36.589332580566406\n", "sell executed @ 38.20000076293945\n", "sell executed @ 43.39666748046875\n", "sell executed @ 47.32600021362305\n", "sell executed @ 48.65533447265625\n", "sell executed @ 49.680667877197266\n", "sell executed @ 50.259334564208984\n", "sell executed @ 49.75733184814453\n", "buy executed @ 45.781333923339844\n", "buy executed @ 48.807334899902344\n", "sell executed @ 47.04199981689453\n", "buy executed @ 48.34333419799805\n", "sell executed @ 53.25\n", "sell executed @ 51.27466583251953\n", "buy executed @ 53.367332458496094\n", "sell executed @ 52.12533187866211\n", "buy executed @ 46.75466537475586\n", "buy executed @ 50.74599838256836\n", "sell executed @ 51.2140007019043\n", "sell executed @ 52.172000885009766\n", "sell executed @ 52.00266647338867\n", "buy executed @ 54.62799835205078\n", "sell executed @ 54.08599853515625\n", "buy executed @ 53.96066665649414\n", "buy executed @ 52.73066711425781\n", "buy executed @ 53.55533218383789\n", "sell executed @ 53.27799987792969\n", "buy executed @ 54.242000579833984\n", "sell executed @ 53.867332458496094\n", "buy executed @ 54.37066650390625\n", "sell executed @ 55.17333221435547\n", "sell executed @ 54.45866775512695\n", "buy executed @ 54.591331481933594\n", "sell executed @ 54.68199920654297\n", "sell executed @ 53.72066879272461\n", "buy executed @ 55.66666793823242\n", "sell executed @ 59.87333297729492\n", "sell executed @ 58.770668029785156\n", "buy executed @ 58.86399841308594\n", "sell executed @ 57.62533187866211\n", "buy executed @ 59.04399871826172\n", "sell executed @ 63.327999114990234\n", "sell executed @ 62.711334228515625\n", "buy executed @ 68.336669921875\n", "sell executed @ 64.85600280761719\n", "buy executed @ 62.35200119018555\n", "buy executed @ 66.05999755859375\n", "sell executed @ 65.47533416748047\n", "buy executed @ 66.11933135986328\n", "sell executed @ 66.9306640625\n", "sell executed @ 66.72666931152344\n", "buy executed @ 66.28800201416016\n", "buy executed @ 66.78533172607422\n", "sell executed @ 64.0566635131836\n", "buy executed @ 65.73200225830078\n", "sell executed @ 63.982666015625\n", "buy executed @ 67.29000091552734\n", "sell executed @ 71.98733520507812\n", "sell executed @ 74.64199829101562\n", "sell executed @ 80.57733154296875\n", "sell executed @ 91.43866729736328\n", "sell executed @ 92.65733337402344\n", "sell executed @ 91.05867004394531\n", "buy executed @ 92.9520034790039\n", "sell executed @ 102.97666931152344\n", "sell executed @ 99.80400085449219\n", "buy executed @ 101.12000274658203\n", "sell executed @ 103.06732940673828\n", "sell executed @ 100.04266357421875\n", "buy executed @ 100.05599975585938\n", "sell executed @ 109.53333282470703\n", "sell executed @ 104.55733489990234\n", "buy executed @ 106.15533447265625\n", "sell executed @ 100.87133026123047\n", "buy executed @ 94.46666717529297\n", "buy executed @ 102.63999938964844\n", "sell executed @ 98.43267059326172\n", "buy executed @ 99.94066619873047\n", "sell executed @ 99.16600036621094\n", "buy executed @ 95.38400268554688\n", "buy executed @ 99.0\n", "sell executed @ 99.13333129882812\n", "sell executed @ 99.00133514404297\n", "buy executed @ 99.30533599853516\n", "sell executed @ 96.84733581542969\n", "buy executed @ 94.57133483886719\n", "buy executed @ 91.6259994506836\n", "buy executed @ 103.65066528320312\n", "sell executed @ 108.06666564941406\n", "sell executed @ 110.04733276367188\n", "sell executed @ 122.3759994506836\n", "sell executed @ 125.80599975585938\n", "sell executed @ 125.23533630371094\n", "buy executed @ 133.45533752441406\n", "sell executed @ 136.6653289794922\n", "sell executed @ 134.27999877929688\n", "buy executed @ 134.8893280029297\n", "sell executed @ 143.54466247558594\n", "sell executed @ 149.25\n", "sell executed @ 147.55999755859375\n", "buy executed @ 166.10667419433594\n", "sell executed @ 158.35000610351562\n", "buy executed @ 149.1233367919922\n", "buy executed @ 135.6666717529297\n", "buy executed @ 139.44000244140625\n", "sell executed @ 110.06999969482422\n", "buy executed @ 122.09333038330078\n", "sell executed @ 123.77999877929688\n", "sell executed @ 124.23999786376953\n", "sell executed @ 139.8733367919922\n", "sell executed @ 149.9199981689453\n", "sell executed @ 147.25332641601562\n", "buy executed @ 141.14332580566406\n", "buy executed @ 147.38333129882812\n", "sell executed @ 149.79666137695312\n", "sell executed @ 141.41000366210938\n", "buy executed @ 126.78666687011719\n", "buy executed @ 129.26333618164062\n", "sell executed @ 135.77999877929688\n", "sell executed @ 140.39999389648438\n", "sell executed @ 139.69000244140625\n", "buy executed @ 143.00332641601562\n", "sell executed @ 149.3866729736328\n", "sell executed @ 138.3633270263672\n", "buy executed @ 141.89332580566406\n", "sell executed @ 137.9933319091797\n", "buy executed @ 141.76666259765625\n", "sell executed @ 141.97332763671875\n", "sell executed @ 144.6666717529297\n", "sell executed @ 147.43333435058594\n", "sell executed @ 148.88333129882812\n", "sell executed @ 153.76666259765625\n", "sell executed @ 149.6266632080078\n", "buy executed @ 146.55667114257812\n", "buy executed @ 143.61000061035156\n", "buy executed @ 140.64666748046875\n", "buy executed @ 140.8800048828125\n", "sell executed @ 141.92999267578125\n", "sell executed @ 140.2100067138672\n", "buy executed @ 140.0933380126953\n", "buy executed @ 141.55999755859375\n", "sell executed @ 135.33999633789062\n", "buy executed @ 136.94332885742188\n", "sell executed @ 129.34666442871094\n", "buy executed @ 133.50332641601562\n", "sell executed @ 141.3000030517578\n", "sell executed @ 140.32666015625\n", "buy executed @ 146.02999877929688\n", "sell executed @ 143.31666564941406\n", "buy executed @ 140.4199981689453\n", "buy executed @ 136.7866668701172\n", "buy executed @ 139.0433349609375\n", "sell executed @ 137.25332641601562\n", "buy executed @ 136.1666717529297\n", "buy executed @ 136.02999877929688\n", "buy executed @ 147.20333862304688\n", "sell executed @ 162.2133331298828\n", "sell executed @ 166.42333984375\n", "sell executed @ 163.20333862304688\n", "buy executed @ 173.9499969482422\n", "sell executed @ 185.1266632080078\n", "sell executed @ 191.3333282470703\n", "sell executed @ 195.25332641601562\n", "sell executed @ 189.1999969482422\n", "buy executed @ 194.9199981689453\n", "sell executed @ 189.60667419433594\n", "buy executed @ 197.7933349609375\n", "sell executed @ 199.67999267578125\n", "sell executed @ 213.9199981689453\n", "sell executed @ 216.6266632080078\n", "sell executed @ 201.4933319091797\n", "buy executed @ 209.02333068847656\n", "sell executed @ 203.3300018310547\n", "buy executed @ 213.27667236328125\n", "sell executed @ 211.0833282470703\n", "buy executed @ 207.58999633789062\n", "buy executed @ 218.63333129882812\n", "sell executed @ 231.6666717529297\n", "sell executed @ 216.6199951171875\n", "buy executed @ 213.44667053222656\n", "buy executed @ 215.32666015625\n", "sell executed @ 220.58999633789062\n", "sell executed @ 221.22999572753906\n", "sell executed @ 221.99667358398438\n", "sell executed @ 231.5933380126953\n", "sell executed @ 235.22332763671875\n", "sell executed @ 243.2566680908203\n", "sell executed @ 245.0366668701172\n", "sell executed @ 251.9933319091797\n", "sell executed @ 272.0133361816406\n", "sell executed @ 293.3399963378906\n", "sell executed @ 270.39666748046875\n", "buy executed @ 283.14666748046875\n", "sell executed @ 284.8033447265625\n", "sell executed @ 281.6666564941406\n", "buy executed @ 275.38665771484375\n", "buy executed @ 281.51666259765625\n", "sell executed @ 283.48333740234375\n", "sell executed @ 281.663330078125\n", "buy executed @ 282.21331787109375\n", "sell executed @ 293.6000061035156\n", "sell executed @ 294.36334228515625\n", "sell executed @ 288.0533447265625\n", "buy executed @ 278.4766540527344\n", "buy executed @ 264.510009765625\n", "buy executed @ 279.9366760253906\n", "sell executed @ 290.92999267578125\n", "sell executed @ 284.89666748046875\n", "buy executed @ 283.3299865722656\n", "buy executed @ 284.07666015625\n", "sell executed @ 287.8066711425781\n", "sell executed @ 283.1533203125\n", "buy executed @ 268.2733459472656\n", "buy executed @ 270.5533447265625\n", "sell executed @ 272.0400085449219\n", "sell executed @ 265.40667724609375\n", "buy executed @ 266.04998779296875\n", "sell executed @ 262.4599914550781\n", "buy executed @ 260.4333190917969\n", "buy executed @ 238.1666717529297\n", "buy executed @ 232.94667053222656\n", "buy executed @ 247.33999633789062\n", "sell executed @ 227.4066619873047\n", "buy executed @ 225.1666717529297\n", "buy executed @ 239.47666931152344\n", "sell executed @ 228.81333923339844\n", "buy executed @ 217.73333740234375\n", "buy executed @ 207.14666748046875\n", "buy executed @ 199.31666564941406\n", "buy executed @ 187.6666717529297\n", "buy executed @ 224.52667236328125\n", "sell executed @ 222.68666076660156\n", "buy executed @ 233.1999969482422\n", "sell executed @ 231.2433319091797\n", "buy executed @ 235.97999572753906\n", "sell executed @ 225.6266632080078\n", "buy executed @ 233.93666076660156\n", "sell executed @ 217.72000122070312\n", "buy executed @ 218.2899932861328\n", "sell executed @ 223.3333282470703\n", "sell executed @ 220.72000122070312\n", "buy executed @ 210.08999633789062\n", "buy executed @ 213.4633331298828\n", "sell executed @ 206.23666381835938\n", "buy executed @ 203.76333618164062\n", "buy executed @ 211.8733367919922\n", "sell executed @ 222.64332580566406\n", "sell executed @ 220.5833282470703\n", "buy executed @ 230.35000610351562\n", "sell executed @ 230.5399932861328\n", "sell executed @ 223.6566619873047\n", "buy executed @ 227.93333435058594\n", "sell executed @ 225.67333984375\n", "buy executed @ 233.9933319091797\n", "sell executed @ 254.10667419433594\n", "sell executed @ 244.07666015625\n", "buy executed @ 246.28334045410156\n", "sell executed @ 246.5933380126953\n", "sell executed @ 238.2100067138672\n", "buy executed @ 239.663330078125\n", "sell executed @ 248.0399932861328\n", "sell executed @ 239.89666748046875\n", "buy executed @ 243.13333129882812\n", "sell executed @ 246.06666564941406\n", "sell executed @ 234.913330078125\n", "buy executed @ 231.46665954589844\n", "buy executed @ 225.6666717529297\n", "buy executed @ 236.47999572753906\n", "sell executed @ 228.3000030517578\n", "buy executed @ 224.53334045410156\n", "buy executed @ 223.64666748046875\n", "buy executed @ 221.17999267578125\n", "buy executed @ 224.1233367919922\n", "sell executed @ 209.67999267578125\n", "buy executed @ 205.73333740234375\n", "buy executed @ 196.6300048828125\n", "buy executed @ 190.56333923339844\n", "buy executed @ 196.5800018310547\n", "sell executed @ 192.27667236328125\n", "buy executed @ 192.6233367919922\n", "sell executed @ 187.82000732421875\n", "buy executed @ 195.5933380126953\n", "sell executed @ 193.6266632080078\n", "buy executed @ 202.14666748046875\n", "sell executed @ 201.56333923339844\n", "buy executed @ 206.3766632080078\n", "sell executed @ 210.28334045410156\n", "sell executed @ 208.4066619873047\n", "buy executed @ 207.96665954589844\n", "buy executed @ 201.7066650390625\n", "buy executed @ 190.94667053222656\n", "buy executed @ 199.68333435058594\n", "sell executed @ 201.7100067138672\n", "sell executed @ 201.19667053222656\n", "buy executed @ 199.5933380126953\n", "buy executed @ 203.3733367919922\n", "sell executed @ 203.29666137695312\n", "buy executed @ 205.89666748046875\n", "sell executed @ 199.7866668701172\n", "buy executed @ 201.6233367919922\n", "sell executed @ 205.53334045410156\n", "sell executed @ 207.77000427246094\n", "sell executed @ 206.94332885742188\n", "buy executed @ 207.90333557128906\n", "sell executed @ 218.85667419433594\n", "sell executed @ 226.60667419433594\n", "sell executed @ 223.9566650390625\n", "buy executed @ 229.57333374023438\n", "sell executed @ 226.9199981689453\n", "buy executed @ 226.56666564941406\n", "buy executed @ 225.97332763671875\n", "buy executed @ 226.3000030517578\n", "sell executed @ 219.86000061035156\n", "buy executed @ 214.88333129882812\n", "buy executed @ 217.60333251953125\n", "sell executed @ 218.98333740234375\n", "sell executed @ 228.56666564941406\n", "sell executed @ 222.84666442871094\n", "buy executed @ 217.7933349609375\n", "buy executed @ 216.86666870117188\n", "buy executed @ 214.74000549316406\n", "buy executed @ 215.4066619873047\n", "sell executed @ 220.1666717529297\n", "sell executed @ 218.42999267578125\n", "buy executed @ 216.4199981689453\n", "buy executed @ 214.4600067138672\n", "buy executed @ 219.2066650390625\n", "sell executed @ 214.92666625976562\n", "buy executed @ 215.66000366210938\n", "sell executed @ 225.78334045410156\n", "sell executed @ 229.06666564941406\n", "sell executed @ 236.55667114257812\n", "sell executed @ 236.5800018310547\n", "sell executed @ 236.97332763671875\n", "sell executed @ 238.2100067138672\n", "sell executed @ 233.03334045410156\n", "buy executed @ 237.9199981689453\n", "sell executed @ 236.663330078125\n", "buy executed @ 235.94000244140625\n", "buy executed @ 240.75\n", "sell executed @ 239.05667114257812\n", "buy executed @ 228.72332763671875\n", "buy executed @ 221.90333557128906\n", "buy executed @ 229.663330078125\n", "sell executed @ 224.49000549316406\n", "buy executed @ 226.75332641601562\n", "sell executed @ 235.43333435058594\n", "sell executed @ 236.163330078125\n", "sell executed @ 237.06666564941406\n", "sell executed @ 233.72000122070312\n", "buy executed @ 237.30667114257812\n", "sell executed @ 243.6366729736328\n", "sell executed @ 245.24000549316406\n", "sell executed @ 244.69667053222656\n", "buy executed @ 244.1300048828125\n", "buy executed @ 244.52333068847656\n", "sell executed @ 250.97332763671875\n", "sell executed @ 251.2899932861328\n", "sell executed @ 251.6199951171875\n", "sell executed @ 245.42333984375\n", "buy executed @ 247.6666717529297\n", "sell executed @ 248.163330078125\n", "sell executed @ 251.94332885742188\n", "sell executed @ 252.3300018310547\n", "sell executed @ 253.163330078125\n", "sell executed @ 243.38999938964844\n", "buy executed @ 246.4600067138672\n", "sell executed @ 250.64666748046875\n", "sell executed @ 251.2133331298828\n", "sell executed @ 258.1300048828125\n", "sell executed @ 263.78668212890625\n", "sell executed @ 259.1866760253906\n", "buy executed @ 260.4366760253906\n", "sell executed @ 258.49334716796875\n", "buy executed @ 258.40667724609375\n", "buy executed @ 260.510009765625\n", "sell executed @ 260.1966552734375\n", "buy executed @ 260.9166564941406\n", "sell executed @ 264.53668212890625\n", "sell executed @ 261.8299865722656\n", "buy executed @ 263.9800109863281\n", "sell executed @ 268.5733337402344\n", "sell executed @ 270.3599853515625\n", "sell executed @ 272.7733459472656\n", "sell executed @ 281.010009765625\n", "sell executed @ 290.03668212890625\n", "sell executed @ 288.0899963378906\n", "buy executed @ 288.6000061035156\n", "sell executed @ 298.0\n", "sell executed @ 303.2266540527344\n", "sell executed @ 341.6199951171875\n", "sell executed @ 339.4766540527344\n", "buy executed @ 345.9533386230469\n", "sell executed @ 359.0133361816406\n", "sell executed @ 371.3333435058594\n", "sell executed @ 402.86334228515625\n", "sell executed @ 390.6666564941406\n", "buy executed @ 404.6199951171875\n", "sell executed @ 409.9700012207031\n", "sell executed @ 407.36334228515625\n", "buy executed @ 387.64666748046875\n", "buy executed @ 341.1666564941406\n", "buy executed @ 355.98333740234375\n", "sell executed @ 354.5033264160156\n", "buy executed @ 344.47332763671875\n", "buy executed @ 337.7966613769531\n", "buy executed @ 351.57666015625\n", "sell executed @ 363.0033264160156\n", "sell executed @ 365.4599914550781\n", "sell executed @ 379.0199890136719\n", "sell executed @ 385.6233215332031\n", "sell executed @ 369.6766662597656\n", "buy executed @ 372.0\n", "sell executed @ 360.6400146484375\n", "buy executed @ 378.9966735839844\n", "sell executed @ 381.586669921875\n", "sell executed @ 365.0\n", "buy executed @ 361.5333251953125\n", "buy executed @ 338.3233337402344\n", "buy executed @ 336.336669921875\n", "buy executed @ 350.5833435058594\n", "sell executed @ 356.32000732421875\n", "sell executed @ 334.6000061035156\n", "buy executed @ 339.010009765625\n", "sell executed @ 322.13665771484375\n", "buy executed @ 319.5033264160156\n", "buy executed @ 325.3299865722656\n", "sell executed @ 308.97332763671875\n", "buy executed @ 310.8566589355469\n", "sell executed @ 299.9800109863281\n", "buy executed @ 312.84332275390625\n", "sell executed @ 336.2900085449219\n", "sell executed @ 355.6666564941406\n", "sell executed @ 364.64666748046875\n", "sell executed @ 362.8233337402344\n", "buy executed @ 362.0633239746094\n", "buy executed @ 356.7799987792969\n", "buy executed @ 352.260009765625\n", "buy executed @ 399.9266662597656\n", "sell executed @ 383.1966552734375\n", "buy executed @ 362.7066650390625\n", "buy executed @ 354.8999938964844\n", "buy executed @ 342.32000732421875\n", "buy executed @ 352.7066650390625\n", "sell executed @ 354.79998779296875\n", "sell executed @ 368.739990234375\n", "sell executed @ 343.85333251953125\n", "buy executed @ 349.8699951171875\n", "sell executed @ 343.5033264160156\n", "buy executed @ 331.8833312988281\n", "buy executed @ 332.0899963378906\n", "sell executed @ 314.6333312988281\n", "buy executed @ 310.0\n", "buy executed @ 306.1333312988281\n", "buy executed @ 312.4700012207031\n", "sell executed @ 276.3666687011719\n", "buy executed @ 282.1166687011719\n", "sell executed @ 312.239990234375\n", "sell executed @ 310.4166564941406\n", "buy executed @ 301.88665771484375\n", "buy executed @ 297.0466613769531\n", "buy executed @ 307.7733459472656\n", "sell executed @ 302.4466552734375\n", "buy executed @ 307.3333435058594\n", "sell executed @ 310.6666564941406\n", "sell executed @ 301.51666259765625\n", "buy executed @ 286.6666564941406\n", "buy executed @ 291.9200134277344\n", "sell executed @ 307.4766540527344\n", "sell executed @ 307.7966613769531\n", "sell executed @ 292.1166687011719\n", "buy executed @ 285.6600036621094\n", "buy executed @ 273.84332275390625\n", "buy executed @ 254.67999267578125\n", "buy executed @ 266.92333984375\n", "sell executed @ 269.9566650390625\n", "sell executed @ 290.1433410644531\n", "sell executed @ 288.1233215332031\n", "buy executed @ 293.2966613769531\n", "sell executed @ 279.7633361816406\n", "buy executed @ 279.42999267578125\n", "buy executed @ 268.1933288574219\n", "buy executed @ 274.79998779296875\n", "sell executed @ 286.3233337402344\n", "sell executed @ 279.4333190917969\n", "buy executed @ 265.1166687011719\n", "buy executed @ 255.4566650390625\n", "buy executed @ 267.2966613769531\n", "sell executed @ 280.07666015625\n", "sell executed @ 290.5333251953125\n", "sell executed @ 301.7966613769531\n", "sell executed @ 307.0533447265625\n", "sell executed @ 331.32666015625\n", "sell executed @ 333.03668212890625\n", "sell executed @ 337.97332763671875\n", "sell executed @ 336.8800048828125\n", "buy executed @ 363.9466552734375\n", "sell executed @ 366.5233459472656\n", "sell executed @ 364.663330078125\n", "buy executed @ 359.20001220703125\n", "buy executed @ 361.5299987792969\n", "sell executed @ 381.8166809082031\n", "sell executed @ 363.7533264160156\n", "buy executed @ 348.586669921875\n", "buy executed @ 352.4200134277344\n", "sell executed @ 341.8299865722656\n", "buy executed @ 325.30999755859375\n", "buy executed @ 328.98333740234375\n", "sell executed @ 340.7900085449219\n", "sell executed @ 328.3333435058594\n", "buy executed @ 334.7633361816406\n", "sell executed @ 342.7166748046875\n", "sell executed @ 325.73333740234375\n", "buy executed @ 336.260009765625\n", "sell executed @ 335.01666259765625\n", "buy executed @ 332.67333984375\n", "buy executed @ 292.1400146484375\n", "buy executed @ 293.836669921875\n", "sell executed @ 292.5033264160156\n", "buy executed @ 290.2533264160156\n", "buy executed @ 300.9800109863281\n", "sell executed @ 303.0833435058594\n", "sell executed @ 317.5400085449219\n", "sell executed @ 291.09332275390625\n", "buy executed @ 288.54998779296875\n", "buy executed @ 262.3699951171875\n", "buy executed @ 266.67999267578125\n", "sell executed @ 244.6666717529297\n", "buy executed @ 242.6666717529297\n", "buy executed @ 256.5299987792969\n", "sell executed @ 241.4566650390625\n", "buy executed @ 253.8699951171875\n", "sell executed @ 236.60333251953125\n", "buy executed @ 236.47332763671875\n", "buy executed @ 221.3000030517578\n", "buy executed @ 224.96665954589844\n", "sell executed @ 209.3866729736328\n", "buy executed @ 219.60000610351562\n", "sell executed @ 235.91000366210938\n", "sell executed @ 253.2100067138672\n", "sell executed @ 252.75332641601562\n", "buy executed @ 246.7899932861328\n", "buy executed @ 258.3333435058594\n", "sell executed @ 234.51666259765625\n", "buy executed @ 238.27999877929688\n", "sell executed @ 238.8866729736328\n", "sell executed @ 241.86666870117188\n", "sell executed @ 239.7066650390625\n", "buy executed @ 232.22999572753906\n", "buy executed @ 215.73666381835938\n", "buy executed @ 220.88999938964844\n", "sell executed @ 233.0\n", "sell executed @ 213.10000610351562\n", "buy executed @ 216.75999450683594\n", "sell executed @ 237.0366668701172\n", "sell executed @ 236.086669921875\n", "buy executed @ 235.07000732421875\n", "buy executed @ 245.7066650390625\n", "sell executed @ 244.9199981689453\n", "buy executed @ 232.663330078125\n", "buy executed @ 228.49000549316406\n", "buy executed @ 224.47332763671875\n", "buy executed @ 227.26333618164062\n", "sell executed @ 233.06666564941406\n", "sell executed @ 231.73333740234375\n", "buy executed @ 244.5433349609375\n", "sell executed @ 250.76333618164062\n", "sell executed @ 234.3433380126953\n", "buy executed @ 233.07000732421875\n", "buy executed @ 237.0399932861328\n", "sell executed @ 238.31333923339844\n", "sell executed @ 240.06666564941406\n", "sell executed @ 240.54666137695312\n", "sell executed @ 245.52999877929688\n", "sell executed @ 247.5\n", "sell executed @ 271.7066650390625\n", "sell executed @ 272.24334716796875\n", "sell executed @ 268.4333190917969\n", "buy executed @ 258.8599853515625\n", "buy executed @ 274.82000732421875\n", "sell executed @ 280.8999938964844\n", "sell executed @ 297.1499938964844\n", "sell executed @ 297.27667236328125\n", "sell executed @ 300.586669921875\n", "sell executed @ 307.39666748046875\n", "sell executed @ 308.6333312988281\n", "sell executed @ 288.1700134277344\n", "buy executed @ 290.42333984375\n", "sell executed @ 283.3333435058594\n", "buy executed @ 294.3566589355469\n", "sell executed @ 286.6300048828125\n", "buy executed @ 300.0299987792969\n", "sell executed @ 309.32000732421875\n", "sell executed @ 306.5633239746094\n", "buy executed @ 303.9966735839844\n", "buy executed @ 302.8699951171875\n", "buy executed @ 296.6666564941406\n", "buy executed @ 289.913330078125\n", "buy executed @ 296.4533386230469\n", "sell executed @ 297.0966796875\n", "sell executed @ 296.07000732421875\n", "buy executed @ 288.0899963378906\n", "buy executed @ 284.82000732421875\n", "buy executed @ 277.70001220703125\n", "buy executed @ 275.6099853515625\n", "buy executed @ 277.1600036621094\n", "sell executed @ 270.2099914550781\n", "buy executed @ 274.4200134277344\n", "sell executed @ 283.70001220703125\n", "sell executed @ 289.260009765625\n", "sell executed @ 299.67999267578125\n", "sell executed @ 304.4200134277344\n", "sell executed @ 292.1300048828125\n", "buy executed @ 302.6099853515625\n", "sell executed @ 303.75\n", "sell executed @ 303.3500061035156\n", "buy executed @ 309.07000732421875\n", "sell executed @ 308.7300109863281\n", "buy executed @ 300.79998779296875\n", "buy executed @ 288.5899963378906\n", "buy executed @ 275.3299865722656\n", "buy executed @ 276.010009765625\n", "sell executed @ 282.94000244140625\n", "sell executed @ 287.80999755859375\n", "sell executed @ 268.2099914550781\n", "buy executed @ 265.25\n", "buy executed @ 242.39999389648438\n", "buy executed @ 249.44000244140625\n", "sell executed @ 240.80999755859375\n", "buy executed @ 238.1300048828125\n", "buy executed @ 223.07000732421875\n", "buy executed @ 222.9600067138672\n", "buy executed @ 216.5\n", "buy executed @ 217.24000549316406\n", "sell executed @ 221.72000122070312\n", "sell executed @ 204.99000549316406\n", "buy executed @ 219.35000610351562\n", "sell executed @ 220.19000244140625\n", "sell executed @ 222.0399932861328\n", "sell executed @ 207.27999877929688\n", "buy executed @ 214.44000244140625\n", "sell executed @ 211.25\n", "buy executed @ 222.4199981689453\n", "sell executed @ 224.63999938964844\n", "sell executed @ 225.08999633789062\n", "sell executed @ 228.52000427246094\n", "sell executed @ 227.5399932861328\n", "buy executed @ 227.82000732421875\n", "sell executed @ 214.97999572753906\n", "buy executed @ 215.30999755859375\n", "sell executed @ 207.47000122070312\n", "buy executed @ 197.0800018310547\n", "buy executed @ 191.3000030517578\n", "buy executed @ 177.58999633789062\n", "buy executed @ 190.72000122070312\n", "sell executed @ 195.97000122070312\n", "sell executed @ 190.9499969482422\n", "buy executed @ 194.4199981689453\n", "sell executed @ 186.9199981689453\n", "buy executed @ 183.1699981689453\n", "buy executed @ 180.19000244140625\n", "buy executed @ 167.8699951171875\n", "buy executed @ 169.91000366210938\n", "sell executed @ 183.1999969482422\n", "sell executed @ 182.86000061035156\n", "buy executed @ 182.9199981689453\n", "sell executed @ 180.8300018310547\n", "buy executed @ 194.6999969482422\n", "sell executed @ 194.6999969482422\n", "sell executed @ 182.4499969482422\n", "buy executed @ 179.82000732421875\n", "buy executed @ 174.0399932861328\n", "buy executed @ 173.44000244140625\n", "buy executed @ 179.0500030517578\n", "sell executed @ 167.82000732421875\n", "buy executed @ 160.9499969482422\n", "buy executed @ 156.8000030517578\n", "buy executed @ 157.6699981689453\n", "sell executed @ 150.22999572753906\n", "buy executed @ 149.8699951171875\n", "buy executed @ 137.8000030517578\n", "buy executed @ 137.57000732421875\n", "buy executed @ 125.3499984741211\n", "buy executed @ 123.1500015258789\n", "buy executed @ 109.0999984741211\n", "buy executed @ 112.70999908447266\n", "sell executed @ 121.81999969482422\n", "sell executed @ 123.18000030517578\n", "sell executed @ 108.0999984741211\n", "buy executed @ 113.63999938964844\n", "sell executed @ 110.33999633789062\n", "buy executed @ 113.05999755859375\n", "sell executed @ 119.7699966430664\n", "sell executed @ 118.8499984741211\n", "buy executed @ 123.22000122070312\n", "sell executed @ 123.55999755859375\n", "sell executed @ 122.4000015258789\n", "buy executed @ 131.49000549316406\n", "sell executed @ 128.77999877929688\n", "buy executed @ 127.16999816894531\n", "buy executed @ 133.4199981689453\n", "sell executed @ 143.75\n", "sell executed @ 143.88999938964844\n", "sell executed @ 144.42999267578125\n", "sell executed @ 160.27000427246094\n", "sell executed @ 177.89999389648438\n", "sell executed @ 166.66000366210938\n", "buy executed @ 173.22000122070312\n", "sell executed @ 181.41000366210938\n", "sell executed @ 188.27000427246094\n", "sell executed @ 189.97999572753906\n", "sell executed @ 194.75999450683594\n", "sell executed @ 196.80999755859375\n", "sell executed @ 201.2899932861328\n", "sell executed @ 207.32000732421875\n", "sell executed @ 196.88999938964844\n", "buy executed @ 194.63999938964844\n", "buy executed @ 209.25\n", "sell executed @ 214.24000549316406\n", "sell executed @ 202.0399932861328\n", "buy executed @ 208.30999755859375\n", "sell executed @ 197.3699951171875\n", "buy executed @ 200.86000061035156\n", "sell executed @ 202.07000732421875\n", "sell executed @ 196.8800048828125\n", "buy executed @ 207.6300048828125\n", "sell executed @ 205.7100067138672\n", "buy executed @ 202.77000427246094\n", "buy executed @ 190.89999389648438\n", "buy executed @ 197.7899932861328\n", "sell executed @ 193.80999755859375\n", "buy executed @ 187.7100067138672\n", "buy executed @ 182.0\n", "buy executed @ 172.9199981689453\n", "buy executed @ 173.44000244140625\n", "sell executed @ 174.47999572753906\n", "sell executed @ 183.25999450683594\n", "sell executed @ 180.4499969482422\n", "buy executed @ 184.1300048828125\n", "sell executed @ 180.1300048828125\n", "buy executed @ 183.25\n", "sell executed @ 197.5800018310547\n", "sell executed @ 191.14999389648438\n", "buy executed @ 192.22000122070312\n", "sell executed @ 190.41000366210938\n", "buy executed @ 191.80999755859375\n", "sell executed @ 189.19000244140625\n", "buy executed @ 193.8800048828125\n", "sell executed @ 195.27999877929688\n", "sell executed @ 207.4600067138672\n", "sell executed @ 194.77000427246094\n", "buy executed @ 192.5800018310547\n", "buy executed @ 185.52000427246094\n", "buy executed @ 185.05999755859375\n", "buy executed @ 184.50999450683594\n", "buy executed @ 186.7899932861328\n", "sell executed @ 180.5399932861328\n", "buy executed @ 185.89999389648438\n", "sell executed @ 185.0\n", "buy executed @ 187.0399932861328\n", "sell executed @ 184.30999755859375\n", "buy executed @ 180.58999633789062\n", "buy executed @ 162.99000549316406\n", "buy executed @ 165.0800018310547\n", "sell executed @ 162.5500030517578\n", "buy executed @ 160.6699981689453\n", "buy executed @ 153.75\n", "buy executed @ 160.19000244140625\n", "sell executed @ 164.30999755859375\n", "sell executed @ 161.8300018310547\n", "buy executed @ 160.30999755859375\n", "buy executed @ 160.61000061035156\n", "sell executed @ 161.1999969482422\n", "sell executed @ 170.05999755859375\n", "sell executed @ 171.7899932861328\n", "sell executed @ 169.14999389648438\n", "buy executed @ 168.5399932861328\n", "buy executed @ 172.0800018310547\n", "sell executed @ 167.97999572753906\n", "buy executed @ 166.35000610351562\n", "buy executed @ 166.52000427246094\n", "sell executed @ 173.86000061035156\n", "sell executed @ 176.88999938964844\n", "sell executed @ 180.13999938964844\n", "sell executed @ 188.8699951171875\n", "sell executed @ 185.77000427246094\n", "buy executed @ 182.89999389648438\n", "buy executed @ 184.47000122070312\n", "sell executed @ 193.1699981689453\n", "sell executed @ 201.16000366210938\n", "sell executed @ 203.92999267578125\n", "sell executed @ 207.52000427246094\n", "sell executed @ 213.97000122070312\n", "sell executed @ 217.61000061035156\n", "sell executed @ 221.30999755859375\n", "sell executed @ 224.57000732421875\n", "sell executed @ 234.86000061035156\n", "sell executed @ 244.39999389648438\n", "sell executed @ 249.8300018310547\n", "sell executed @ 258.7099914550781\n", "sell executed @ 256.7900085449219\n", "buy executed @ 255.89999389648438\n", "buy executed @ 260.5400085449219\n", "sell executed @ 274.45001220703125\n", "sell executed @ 259.4599914550781\n", "buy executed @ 264.6099853515625\n", "sell executed @ 256.6000061035156\n", "buy executed @ 241.0500030517578\n", "buy executed @ 250.2100067138672\n", "sell executed @ 256.239990234375\n", "sell executed @ 257.5\n", "sell executed @ 261.7699890136719\n", "sell executed @ 279.82000732421875\n", "sell executed @ 282.4800109863281\n", "sell executed @ 276.5400085449219\n", "buy executed @ 274.42999267578125\n", "buy executed @ 269.6099853515625\n", "buy executed @ 269.7900085449219\n", "sell executed @ 271.989990234375\n", "sell executed @ 277.8999938964844\n", "sell executed @ 281.3800048828125\n", "sell executed @ 290.3800048828125\n", "sell executed @ 293.3399963378906\n", "sell executed @ 291.260009765625\n", "buy executed @ 262.8999938964844\n", "buy executed @ 260.0199890136719\n", "buy executed @ 269.05999755859375\n", "sell executed @ 265.2799987792969\n", "buy executed @ 264.3500061035156\n", "buy executed @ 255.7100067138672\n", "buy executed @ 266.44000244140625\n", "sell executed @ 267.42999267578125\n", "sell executed @ 261.07000732421875\n", "buy executed @ 254.11000061035156\n", "buy executed @ 259.32000732421875\n", "sell executed @ 253.86000061035156\n", "buy executed @ 251.4499969482422\n", "buy executed @ 249.6999969482422\n", "buy executed @ 242.19000244140625\n", "buy executed @ 245.33999633789062\n", "sell executed @ 242.64999389648438\n", "buy executed @ 239.75999450683594\n", "buy executed @ 232.9600067138672\n", "buy executed @ 225.60000610351562\n", "buy executed @ 219.22000122070312\n", "buy executed @ 215.49000549316406\n", "buy executed @ 231.27999877929688\n", "sell executed @ 233.19000244140625\n", "sell executed @ 236.86000061035156\n", "sell executed @ 230.0399932861328\n", "buy executed @ 238.58999633789062\n", "sell executed @ 238.82000732421875\n", "sell executed @ 257.17999267578125\n", "sell executed @ 256.8999938964844\n", "buy executed @ 258.0799865722656\n", "sell executed @ 245.00999450683594\n", "buy executed @ 256.489990234375\n", "sell executed @ 251.9199981689453\n", "buy executed @ 251.49000549316406\n", "buy executed @ 248.5\n", "buy executed @ 273.5799865722656\n", "sell executed @ 267.4800109863281\n", "buy executed @ 271.29998779296875\n", "sell executed @ 276.0400085449219\n", "sell executed @ 274.3900146484375\n", "buy executed @ 265.2799987792969\n", "buy executed @ 266.5\n", "sell executed @ 262.5899963378906\n", "buy executed @ 255.6999969482422\n", "buy executed @ 244.8800048828125\n", "buy executed @ 246.99000549316406\n", "sell executed @ 244.1199951171875\n", "buy executed @ 240.5\n", "buy executed @ 246.3800048828125\n", "sell executed @ 250.22000122070312\n", "sell executed @ 251.60000610351562\n", "sell executed @ 246.52999877929688\n", "buy executed @ 261.1600036621094\n", "sell executed @ 260.04998779296875\n", "buy executed @ 260.5299987792969\n", "sell executed @ 259.6700134277344\n", "buy executed @ 263.6199951171875\n", "sell executed @ 262.989990234375\n", "buy executed @ 258.8699951171875\n", "buy executed @ 251.1199951171875\n", "buy executed @ 253.9199981689453\n", "sell executed @ 254.85000610351562\n", "sell executed @ 242.67999267578125\n", "buy executed @ 220.11000061035156\n", "buy executed @ 211.99000549316406\n", "buy executed @ 212.0800018310547\n", "sell executed @ 216.52000427246094\n", "sell executed @ 212.4199981689453\n", "buy executed @ 205.75999450683594\n", "buy executed @ 207.3000030517578\n", "sell executed @ 197.36000061035156\n", "buy executed @ 200.83999633789062\n", "sell executed @ 205.66000366210938\n", "sell executed @ 218.50999450683594\n", "sell executed @ 219.9600067138672\n", "sell executed @ 219.27000427246094\n", "buy executed @ 222.17999267578125\n", "sell executed @ 222.11000061035156\n", "buy executed @ 209.97999572753906\n", "buy executed @ 214.64999389648438\n", "sell executed @ 223.7100067138672\n", "sell executed @ 237.41000366210938\n", "sell executed @ 242.83999633789062\n", "sell executed @ 233.58999633789062\n", "buy executed @ 234.3000030517578\n", "sell executed @ 235.60000610351562\n", "sell executed @ 241.1999969482422\n", "sell executed @ 234.2100067138672\n", "buy executed @ 235.4499969482422\n", "sell executed @ 236.0800018310547\n", "sell executed @ 246.72000122070312\n", "sell executed @ 244.13999938964844\n", "buy executed @ 240.0800018310547\n", "buy executed @ 238.8300018310547\n", "buy executed @ 235.5800018310547\n", "buy executed @ 238.72000122070312\n", "sell executed @ 239.3699951171875\n", "sell executed @ 242.63999938964844\n", "sell executed @ 243.83999633789062\n", "sell executed @ 239.74000549316406\n", "buy executed @ 237.00999450683594\n", "buy executed @ 239.2899932861328\n", "sell executed @ 251.0500030517578\n", "sell executed @ 253.5\n", "sell executed @ 252.0800018310547\n", "buy executed @ 257.2200012207031\n", "sell executed @ 247.13999938964844\n", "buy executed @ 254.5\n", "sell executed @ 252.5399932861328\n", "buy executed @ 256.6099853515625\n", "sell executed @ 261.44000244140625\n", "sell executed @ 253.17999267578125\n", "buy executed @ 248.47999572753906\n", "buy executed @ 248.4199981689453\n", "buy executed @ 238.4499969482422\n", "buy executed @ 237.92999267578125\n", "buy executed @ 237.49000549316406\n", "buy executed @ 240.4499969482422\n", "sell executed @ 234.9600067138672\n", "buy executed @ 233.94000244140625\n", "buy executed @ 227.22000122070312\n", "buy executed @ 218.88999938964844\n", "buy executed @ 219.91000366210938\n", "sell executed @ 215.5500030517578\n", "buy executed @ 211.8800048828125\n", "buy executed @ 212.19000244140625\n", "sell executed @ 208.8000030517578\n", "buy executed @ 209.13999938964844\n", "sell executed @ 207.8300018310547\n", "buy executed @ 182.6300048828125\n", "buy executed @ 183.25\n", "sell executed @ 190.92999267578125\n", "sell executed @ 191.58999633789062\n", "sell executed @ 187.2899932861328\n", "buy executed @ 188.86000061035156\n", "sell executed @ 187.91000366210938\n", "buy executed @ 181.05999755859375\n", "buy executed @ 185.10000610351562\n", "sell executed @ 187.5800018310547\n", "sell executed @ 189.55999755859375\n", "sell executed @ 193.57000732421875\n", "sell executed @ 188.1300048828125\n", "buy executed @ 184.02000427246094\n", "buy executed @ 188.7100067138672\n", "sell executed @ 200.4499969482422\n", "sell executed @ 199.9499969482422\n", "buy executed @ 193.75999450683594\n", "buy executed @ 194.77000427246094\n", "sell executed @ 197.41000366210938\n", "sell executed @ 191.97000122070312\n", "buy executed @ 199.39999389648438\n", "sell executed @ 199.72999572753906\n", "sell executed @ 202.0399932861328\n", "sell executed @ 201.8800048828125\n", "buy executed @ 202.63999938964844\n", "sell executed @ 188.13999938964844\n", "buy executed @ 180.74000549316406\n", "buy executed @ 176.5399932861328\n", "buy executed @ 178.64999389648438\n", "sell executed @ 175.33999633789062\n", "buy executed @ 177.77000427246094\n", "sell executed @ 177.5399932861328\n", "buy executed @ 169.47999572753906\n", "buy executed @ 162.5\n", "buy executed @ 163.57000732421875\n", "sell executed @ 173.8000030517578\n", "sell executed @ 171.32000732421875\n", "buy executed @ 175.66000366210938\n", "sell executed @ 172.82000732421875\n", "buy executed @ 170.8300018310547\n", "buy executed @ 172.6300048828125\n", "sell executed @ 177.6699981689453\n", "sell executed @ 179.8300018310547\n", "sell executed @ 175.7899932861328\n", "buy executed @ 175.22000122070312\n", "buy executed @ 166.6300048828125\n", "buy executed @ 168.3800048828125\n", "sell executed @ 171.11000061035156\n", "sell executed @ 164.89999389648438\n", "buy executed @ 172.97999572753906\n", "sell executed @ 176.8800048828125\n", "sell executed @ 171.75999450683594\n", "buy executed @ 174.60000610351562\n", "sell executed @ 171.0500030517578\n", "buy executed @ 161.47999572753906\n", "buy executed @ 157.11000061035156\n", "buy executed @ 155.4499969482422\n", "buy executed @ 149.92999267578125\n", "buy executed @ 147.0500030517578\n", "buy executed @ 142.0500030517578\n", "buy executed @ 144.67999267578125\n", "sell executed @ 162.1300048828125\n", "sell executed @ 170.17999267578125\n", "sell executed @ 168.2899932861328\n", "buy executed @ 194.0500030517578\n", "sell executed @ 183.27999877929688\n", "buy executed @ 179.99000549316406\n", "buy executed @ 180.00999450683594\n", "sell executed @ 181.19000244140625\n", "sell executed @ 184.75999450683594\n", "sell executed @ 177.80999755859375\n", "buy executed @ 174.72000122070312\n", "buy executed @ 171.97000122070312\n", "buy executed @ 168.47000122070312\n", "buy executed @ 171.88999938964844\n", "sell executed @ 177.5500030517578\n", "sell executed @ 173.99000549316406\n", "buy executed @ 174.83999633789062\n", "sell executed @ 177.4600067138672\n", "sell executed @ 174.9499969482422\n", "buy executed @ 186.60000610351562\n", "sell executed @ 180.11000061035156\n", "buy executed @ 173.74000549316406\n", "buy executed @ 179.24000549316406\n", "sell executed @ 176.75\n", "buy executed @ 176.19000244140625\n", "buy executed @ 178.7899932861328\n", "sell executed @ 178.0800018310547\n", "buy executed @ 176.2899932861328\n", "buy executed @ 174.77000427246094\n", "buy executed @ 175.0\n", "sell executed @ 177.94000244140625\n", "sell executed @ 177.47999572753906\n", "buy executed @ 173.7899932861328\n", "buy executed @ 170.66000366210938\n", "buy executed @ 177.2899932861328\n", "sell executed @ 182.47000122070312\n", "sell executed @ 178.00999450683594\n", "buy executed @ 187.44000244140625\n", "sell executed @ 184.86000061035156\n", "buy executed @ 181.57000732421875\n", "buy executed @ 183.00999450683594\n", "sell executed @ 182.5800018310547\n", "buy executed @ 187.35000610351562\n", "sell executed @ 196.3699951171875\n", "sell executed @ 197.4199981689453\n", "sell executed @ 197.8800048828125\n", "sell executed @ 209.86000061035156\n", "sell executed @ 231.25999450683594\n", "sell executed @ 246.38999938964844\n", "sell executed @ 251.52000427246094\n", "sell executed @ 252.94000244140625\n", "sell executed @ 262.3299865722656\n", "sell executed @ 263.260009765625\n", "sell executed @ 241.02999877929688\n", "buy executed @ 248.22999572753906\n", "sell executed @ 252.63999938964844\n", "sell executed @ 256.55999755859375\n", "sell executed @ 248.5\n", "buy executed @ 249.22999572753906\n", "sell executed @ 239.1999969482422\n", "buy executed @ 251.50999450683594\n", "sell executed @ 246.3800048828125\n", "buy executed @ 215.99000549316406\n", "buy executed @ 220.25\n", "sell executed @ 219.8000030517578\n", "buy executed @ 232.10000610351562\n", "sell executed @ 222.6199951171875\n", "buy executed @ 232.07000732421875\n", "sell executed @ 216.86000061035156\n", "buy executed @ 207.6699981689453\n", "buy executed @ 198.8800048828125\n", "buy executed @ 200.63999938964844\n", "sell executed @ 191.75999450683594\n", "buy executed @ 198.83999633789062\n", "sell executed @ 200.0\n", "sell executed @ 197.49000549316406\n", "buy executed @ 207.8300018310547\n", "sell executed @ 201.3800048828125\n", "buy executed @ 214.13999938964844\n", "sell executed @ 216.1199951171875\n"]}, {"data": {"text/plain": ["[<__main__.momentum at 0x204d34eef00>]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro.run()"]}, {"cell_type": "code", "execution_count": 6, "id": "f29a32dd-9314-4ea3-b2ce-add507d2de04", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n"]}], "source": ["eur_usd_hourly = yf.download(tickers='EURUSD=X', start='2023-01-01', interval='1h')"]}, {"cell_type": "code", "execution_count": 7, "id": "3dfa4373-c782-4ed7-95e5-c8e9d185d179", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 10082 entries, 2023-01-02 00:00:00+00:00 to 2024-08-16 22:00:00+01:00\n", "Data columns (total 6 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   Open       10082 non-null  float64\n", " 1   High       10082 non-null  float64\n", " 2   Low        10082 non-null  float64\n", " 3   Close      10082 non-null  float64\n", " 4   Adj Close  10082 non-null  float64\n", " 5   Volume     10082 non-null  int64  \n", "dtypes: float64(5), int64(1)\n", "memory usage: 551.4 KB\n"]}], "source": ["eur_usd_hourly.info()"]}, {"cell_type": "code", "execution_count": 8, "id": "fd6ea759-316c-4927-b054-e324e6fc27f9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-02 00:00:00+00:00</th>\n", "      <td>1.070091</td>\n", "      <td>1.071123</td>\n", "      <td>1.069862</td>\n", "      <td>1.070893</td>\n", "      <td>1.070893</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 01:00:00+00:00</th>\n", "      <td>1.071008</td>\n", "      <td>1.071237</td>\n", "      <td>1.070091</td>\n", "      <td>1.070664</td>\n", "      <td>1.070664</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 02:00:00+00:00</th>\n", "      <td>1.070435</td>\n", "      <td>1.070435</td>\n", "      <td>1.069633</td>\n", "      <td>1.070435</td>\n", "      <td>1.070435</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 03:00:00+00:00</th>\n", "      <td>1.070549</td>\n", "      <td>1.070893</td>\n", "      <td>1.070091</td>\n", "      <td>1.070893</td>\n", "      <td>1.070893</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 04:00:00+00:00</th>\n", "      <td>1.070778</td>\n", "      <td>1.071123</td>\n", "      <td>1.069862</td>\n", "      <td>1.070091</td>\n", "      <td>1.070091</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 18:00:00+01:00</th>\n", "      <td>1.100715</td>\n", "      <td>1.101564</td>\n", "      <td>1.100715</td>\n", "      <td>1.101564</td>\n", "      <td>1.101564</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 19:00:00+01:00</th>\n", "      <td>1.101564</td>\n", "      <td>1.102414</td>\n", "      <td>1.101200</td>\n", "      <td>1.102414</td>\n", "      <td>1.102414</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 20:00:00+01:00</th>\n", "      <td>1.102293</td>\n", "      <td>1.102779</td>\n", "      <td>1.102171</td>\n", "      <td>1.102779</td>\n", "      <td>1.102779</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 21:00:00+01:00</th>\n", "      <td>1.102779</td>\n", "      <td>1.103266</td>\n", "      <td>1.102536</td>\n", "      <td>1.103022</td>\n", "      <td>1.103022</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 22:00:00+01:00</th>\n", "      <td>1.103144</td>\n", "      <td>1.103144</td>\n", "      <td>1.103144</td>\n", "      <td>1.103144</td>\n", "      <td>1.103144</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10082 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                               Open      High       Low     Close  Adj Close  \\\n", "Datetime                                                                       \n", "2023-01-02 00:00:00+00:00  1.070091  1.071123  1.069862  1.070893   1.070893   \n", "2023-01-02 01:00:00+00:00  1.071008  1.071237  1.070091  1.070664   1.070664   \n", "2023-01-02 02:00:00+00:00  1.070435  1.070435  1.069633  1.070435   1.070435   \n", "2023-01-02 03:00:00+00:00  1.070549  1.070893  1.070091  1.070893   1.070893   \n", "2023-01-02 04:00:00+00:00  1.070778  1.071123  1.069862  1.070091   1.070091   \n", "...                             ...       ...       ...       ...        ...   \n", "2024-08-16 18:00:00+01:00  1.100715  1.101564  1.100715  1.101564   1.101564   \n", "2024-08-16 19:00:00+01:00  1.101564  1.102414  1.101200  1.102414   1.102414   \n", "2024-08-16 20:00:00+01:00  1.102293  1.102779  1.102171  1.102779   1.102779   \n", "2024-08-16 21:00:00+01:00  1.102779  1.103266  1.102536  1.103022   1.103022   \n", "2024-08-16 22:00:00+01:00  1.103144  1.103144  1.103144  1.103144   1.103144   \n", "\n", "                           Volume  \n", "Datetime                           \n", "2023-01-02 00:00:00+00:00       0  \n", "2023-01-02 01:00:00+00:00       0  \n", "2023-01-02 02:00:00+00:00       0  \n", "2023-01-02 03:00:00+00:00       0  \n", "2023-01-02 04:00:00+00:00       0  \n", "...                           ...  \n", "2024-08-16 18:00:00+01:00       0  \n", "2024-08-16 19:00:00+01:00       0  \n", "2024-08-16 20:00:00+01:00       0  \n", "2024-08-16 21:00:00+01:00       0  \n", "2024-08-16 22:00:00+01:00       0  \n", "\n", "[10082 rows x 6 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["eur_usd_hourly"]}, {"cell_type": "code", "execution_count": 9, "id": "175159ad-5f22-410e-b3df-5750cbf274d1", "metadata": {}, "outputs": [], "source": ["df = eur_usd_hourly[['Adj Close']]\n", "df.columns = ['price']"]}, {"cell_type": "code", "execution_count": 10, "id": "74319490-a54e-4c3d-ad2a-71ebceb52f75", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10260\\1029451938.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df.loc[:, 'return'] = np.log(df.price / df.price.shift(1))\n"]}], "source": ["df.loc[:, 'return'] = np.log(df.price / df.price.shift(1))"]}, {"cell_type": "code", "execution_count": 11, "id": "61b7faa9-7b05-477d-9ee1-a985e8f080ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "      <th>return</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-02 00:00:00+00:00</th>\n", "      <td>1.070893</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 01:00:00+00:00</th>\n", "      <td>1.070664</td>\n", "      <td>-0.000214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 02:00:00+00:00</th>\n", "      <td>1.070435</td>\n", "      <td>-0.000214</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 03:00:00+00:00</th>\n", "      <td>1.070893</td>\n", "      <td>0.000428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-02 04:00:00+00:00</th>\n", "      <td>1.070091</td>\n", "      <td>-0.000749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 18:00:00+01:00</th>\n", "      <td>1.101564</td>\n", "      <td>0.000771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 19:00:00+01:00</th>\n", "      <td>1.102414</td>\n", "      <td>0.000771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 20:00:00+01:00</th>\n", "      <td>1.102779</td>\n", "      <td>0.000331</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 21:00:00+01:00</th>\n", "      <td>1.103022</td>\n", "      <td>0.000221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16 22:00:00+01:00</th>\n", "      <td>1.103144</td>\n", "      <td>0.000110</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10082 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                              price    return\n", "Datetime                                     \n", "2023-01-02 00:00:00+00:00  1.070893       NaN\n", "2023-01-02 01:00:00+00:00  1.070664 -0.000214\n", "2023-01-02 02:00:00+00:00  1.070435 -0.000214\n", "2023-01-02 03:00:00+00:00  1.070893  0.000428\n", "2023-01-02 04:00:00+00:00  1.070091 -0.000749\n", "...                             ...       ...\n", "2024-08-16 18:00:00+01:00  1.101564  0.000771\n", "2024-08-16 19:00:00+01:00  1.102414  0.000771\n", "2024-08-16 20:00:00+01:00  1.102779  0.000331\n", "2024-08-16 21:00:00+01:00  1.103022  0.000221\n", "2024-08-16 22:00:00+01:00  1.103144  0.000110\n", "\n", "[10082 rows x 2 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 12, "id": "74fa05f3-7319-4c58-b765-5db67715f66e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Datetime'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df['return'].groupby(df.index.hour).mean().plot(kind='bar')"]}, {"cell_type": "code", "execution_count": 13, "id": "7df31894-da15-44c0-9c59-387a037ae1d4", "metadata": {}, "outputs": [], "source": ["class HourlyBias(bt.Strategy):\n", "    def __init__(self):\n", "        self.dataclose = self.datas[0].close\n", "    def next(self):\n", "#        print(f'time=  {self.datas[0].datetime.datetime().hour}')\n", "        if self.datas[0].datetime.datetime().hour == 11:\n", "            self.order = self.sell()\n", "            print(f'@time {self.datas[0].datetime.datetime().hour} selling @{self.dataclose[0]}')\n", "        if self.datas[0].datetime.datetime().hour == 13:\n", "            self.order = self.buy()\n", "            print(f'@time {self.datas[0].datetime.datetime().hour} buying @{self.dataclose[0]}')"]}, {"cell_type": "code", "execution_count": 14, "id": "226595fb-0ed4-4108-95f3-921dfd091604", "metadata": {}, "outputs": [], "source": ["data = bt.feeds.PandasData(dataname = df,\n", "                           open = 0,\n", "                           high = 0,\n", "                           low = 0,\n", "                           close = 0,\n", "                           volume =-1,\n", "                           openinterest = -1)"]}, {"cell_type": "code", "execution_count": 15, "id": "a9f130f7-52ff-4012-b4dc-baf93b362009", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.adddata(data)\n", "cerebro.addstrategy(HourlyBias)"]}, {"cell_type": "code", "execution_count": 16, "id": "3e209c40-06c5-4875-8764-8768974a9819", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@time 11 selling @1.0688328742980957\n", "@time 13 buying @1.068261981010437\n", "@time 11 selling @1.0530749559402466\n", "@time 13 buying @1.0561892986297607\n", "@time 11 selling @1.0610079765319824\n", "@time 13 buying @1.0613458156585693\n", "@time 11 selling @1.0614584684371948\n", "@time 13 buying @1.0557432174682617\n", "@time 11 selling @1.0498688220977783\n", "@time 13 buying @1.0547411441802979\n", "@time 11 selling @1.0686043500900269\n", "@time 13 buying @1.0728462934494019\n", "@time 11 selling @1.0738831758499146\n", "@time 13 buying @1.0725010633468628\n", "@time 11 selling @1.0750375986099243\n", "@time 13 buying @1.0752688646316528\n", "@time 11 selling @1.0764262676239014\n", "@time 13 buying @1.0831888914108276\n", "@time 11 selling @1.0814317464828491\n", "@time 13 buying @1.0797970294952393\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0834236145019531\n", "@time 13 buying @1.084481120109558\n", "@time 11 selling @1.0817828178405762\n", "@time 13 buying @1.086130142211914\n", "@time 11 selling @1.082602620124817\n", "@time 13 buying @1.08119797706604\n", "@time 11 selling @1.0829542875289917\n", "@time 13 buying @1.080964207649231\n", "@time 11 selling @1.0884946584701538\n", "@time 13 buying @1.086130142211914\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.08695650100708\n", "@time 11 selling @1.08672034740448\n", "@time 13 buying @1.0874292850494385\n", "@time 11 selling @1.0906314849853516\n", "@time 13 buying @1.0905125141143799\n", "@time 11 selling @1.088020920753479\n", "@time 13 buying @1.08672034740448\n", "@time 11 selling @1.0899182558059692\n", "@time 13 buying @1.0889687538146973\n", "@time 11 selling @1.0836584568023682\n", "@time 13 buying @1.0853049755096436\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0907504558563232\n", "@time 11 selling @1.1003520488739014\n", "@time 13 buying @1.0949305295944214\n", "@time 11 selling @1.093135118484497\n", "@time 13 buying @1.0841283798217773\n", "@time 11 selling @1.0768898725509644\n", "@time 13 buying @1.0761945247650146\n", "@time 11 selling @1.070778489112854\n", "@time 13 buying @1.0698620080947876\n", "@time 11 selling @1.0739984512329102\n", "@time 13 buying @1.0734220743179321\n", "@time 11 selling @1.0765421390533447\n", "@time 13 buying @1.079214334487915\n", "@time 11 selling @1.0702054500579834\n", "@time 13 buying @1.0700910091400146\n", "@time 11 selling @1.0678056478500366\n", "@time 13 buying @1.068376064300537\n", "@time 11 selling @1.0757315158843994\n", "@time 13 buying @1.0761945247650146\n", "@time 11 selling @1.0714668035507202\n", "@time 13 buying @1.068376064300537\n", "@time 11 selling @1.070778489112854\n", "@time 13 buying @1.068261981010437\n", "@time 11 selling @1.0628122091293335\n", "@time 13 buying @1.0643960237503052\n", "@time 11 selling @1.0686043500900269\n", "@time 13 buying @1.068376064300537\n", "@time 11 selling @1.0660980939865112\n", "@time 13 buying @1.0656436681747437\n", "@time 11 selling @1.06326425075531\n", "@time 13 buying @1.0649627447128296\n", "@time 11 selling @1.0606703758239746\n", "@time 13 buying @1.0612331628799438\n", "@time 11 selling @1.0583131313323975\n", "@time 13 buying @1.0557432174682617\n", "@time 11 selling @1.0564124584197998\n", "@time 13 buying @1.0584250688552856\n", "@time 11 selling @1.0611205101013184\n", "@time 13 buying @1.0633772611618042\n", "@time 11 selling @1.0673497915267944\n", "@time 13 buying @1.0676915645599365\n", "@time 11 selling @1.0628122091293335\n", "@time 13 buying @1.0587612390518188\n", "@time 11 selling @1.0616837739944458\n", "@time 13 buying @1.0625863075256348\n", "@time 11 selling @1.0642826557159424\n", "@time 13 buying @1.0656436681747437\n", "@time 11 selling @1.0659844875335693\n", "@time 13 buying @1.0656436681747437\n", "@time 11 selling @1.0540739297866821\n", "@time 13 buying @1.0544073581695557\n", "@time 11 selling @1.0574177503585815\n", "@time 13 buying @1.0574177503585815\n", "@time 11 selling @1.0593219995498657\n", "@time 13 buying @1.0625863075256348\n", "@time 11 selling @1.066894292831421\n", "@time 13 buying @1.0705492496490479\n", "@time 11 selling @1.0723860263824463\n", "@time 13 buying @1.0708931684494019\n", "@time 11 selling @1.0586491823196411\n", "@time 13 buying @1.0549635887145996\n", "@time 11 selling @1.0614584684371948\n", "@time 13 buying @1.0613458156585693\n", "@time 11 selling @1.063151240348816\n", "@time 13 buying @1.063151240348816\n", "@time 11 selling @1.0708931684494019\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.0780508518218994\n", "@time 13 buying @1.0780508518218994\n", "@time 11 selling @1.0800302028656006\n", "@time 13 buying @1.078283429145813\n", "@time 11 selling @1.087902545928955\n", "@time 13 buying @1.0888501405715942\n", "@time 11 selling @1.0739984512329102\n", "@time 13 buying @1.0760787725448608\n", "@time 11 selling @1.07758629322052\n", "@time 13 buying @1.0788650512695312\n", "@time 11 selling @1.0835410356521606\n", "@time 13 buying @1.0828369855880737\n", "@time 11 selling @1.0857763290405273\n", "@time 13 buying @1.0849517583847046\n", "@time 11 selling @1.088139295578003\n", "@time 13 buying @1.091941475868225\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.089680790901184\n", "@time 11 selling @1.0876659154891968\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.0905125141143799\n", "@time 13 buying @1.0906314849853516\n", "@time 11 selling @1.0945708751678467\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0899182558059692\n", "@time 11 selling @1.0920606851577759\n", "@time 13 buying @1.0903936624526978\n", "@time 11 selling @1.0892059803009033\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.090869426727295\n", "@time 13 buying @1.0924185514450073\n", "@time 11 selling @1.0925379991531372\n", "@time 13 buying @1.0976948738098145\n", "@time 11 selling @1.1013215780258179\n", "@time 13 buying @1.1066844463348389\n", "@time 11 selling @1.1057054996490479\n", "@time 13 buying @1.1033873558044434\n", "@time 11 selling @1.098056435585022\n", "@time 13 buying @1.0936132669448853\n", "@time 11 selling @1.0969723463058472\n", "@time 13 buying @1.0961307287216187\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0963709354400635\n", "@time 11 selling @1.095650315284729\n", "@time 13 buying @1.0975743532180786\n", "@time 11 selling @1.0984183549880981\n", "@time 13 buying @1.0960105657577515\n", "@time 11 selling @1.100473165512085\n", "@time 13 buying @1.1027790307998657\n", "@time 11 selling @1.1020498275756836\n", "@time 13 buying @1.1002310514450073\n", "@time 11 selling @1.1048502922058105\n", "@time 13 buying @1.1086474657058716\n", "@time 11 selling @1.1037527322769165\n", "@time 13 buying @1.1013215780258179\n", "@time 11 selling @1.0975743532180786\n", "@time 13 buying @1.1002310514450073\n", "@time 11 selling @1.1015641689300537\n", "@time 13 buying @1.102292776107788\n", "@time 11 selling @1.0969723463058472\n", "@time 13 buying @1.0948106050491333\n", "@time 11 selling @1.1042402982711792\n", "@time 13 buying @1.1033873558044434\n", "@time 11 selling @1.1079105138778687\n", "@time 13 buying @1.1027790307998657\n", "@time 11 selling @1.1013215780258179\n", "@time 13 buying @1.0996261835098267\n", "@time 11 selling @1.1037527322769165\n", "@time 13 buying @1.1046061515808105\n", "@time 11 selling @1.096611499786377\n", "@time 13 buying @1.096611499786377\n", "@time 11 selling @1.0952903032302856\n", "@time 13 buying @1.0991426706314087\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0905125141143799\n", "@time 11 selling @1.0894432067871094\n", "@time 13 buying @1.0893245935440063\n", "@time 11 selling @1.088139295578003\n", "@time 13 buying @1.0882576704025269\n", "@time 11 selling @1.0889687538146973\n", "@time 13 buying @1.088139295578003\n", "@time 11 selling @1.0828369855880737\n", "@time 13 buying @1.0817828178405762\n", "@time 11 selling @1.0817828178405762\n", "@time 13 buying @1.0780508518218994\n", "@time 11 selling @1.08119797706604\n", "@time 13 buying @1.0797970294952393\n", "@time 11 selling @1.0827198028564453\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.077702283859253\n", "@time 13 buying @1.0788650512695312\n", "@time 11 selling @1.078399658203125\n", "@time 13 buying @1.0773539543151855\n", "@time 11 selling @1.0735372304916382\n", "@time 13 buying @1.0715816020965576\n", "@time 11 selling @1.0755001306533813\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.0713521242141724\n", "@time 13 buying @1.071237325668335\n", "@time 11 selling @1.0749220848083496\n", "@time 13 buying @1.0734220743179321\n", "@time 11 selling @1.068261981010437\n", "@time 13 buying @1.0694043636322021\n", "@time 11 selling @1.0708931684494019\n", "@time 13 buying @1.0731916427612305\n", "@time 11 selling @1.0761945247650146\n", "@time 13 buying @1.0744600296020508\n", "@time 11 selling @1.0691757202148438\n", "@time 13 buying @1.0684902667999268\n", "@time 11 selling @1.068261981010437\n", "@time 13 buying @1.0679197311401367\n", "@time 11 selling @1.071926236152649\n", "@time 13 buying @1.0739984512329102\n", "@time 11 selling @1.0738831758499146\n", "@time 13 buying @1.0768898725509644\n", "@time 11 selling @1.07758629322052\n", "@time 13 buying @1.0773539543151855\n", "@time 11 selling @1.0765421390533447\n", "@time 13 buying @1.0756157636642456\n", "@time 11 selling @1.0796804428100586\n", "@time 13 buying @1.0796804428100586\n", "@time 11 selling @1.0806137323379517\n", "@time 13 buying @1.0848339796066284\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0901559591293335\n", "@time 11 selling @1.0963709354400635\n", "@time 13 buying @1.0955302715301514\n", "@time 11 selling @1.0924185514450073\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.093135118484497\n", "@time 13 buying @1.091941475868225\n", "@time 11 selling @1.0927767753601074\n", "@time 13 buying @1.0930156707763672\n", "@time 11 selling @1.0992635488510132\n", "@time 13 buying @1.098659634590149\n", "@time 11 selling @1.088020920753479\n", "@time 13 buying @1.0886130332946777\n", "@time 11 selling @1.0920606851577759\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.0948106050491333\n", "@time 13 buying @1.0968520641326904\n", "@time 11 selling @1.0943313837051392\n", "@time 13 buying @1.0937328338623047\n", "@time 11 selling @1.0942115783691406\n", "@time 13 buying @1.0871928930282593\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.091941475868225\n", "@time 11 selling @1.0901559591293335\n", "@time 13 buying @1.0915838479995728\n", "@time 11 selling @1.0901559591293335\n", "@time 13 buying @1.090274691581726\n", "@time 11 selling @1.0882576704025269\n", "@time 13 buying @1.0894432067871094\n", "@time 11 selling @1.0897995233535767\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.0890873670578003\n", "@time 13 buying @1.0920606851577759\n", "@time 11 selling @1.0957703590393066\n", "@time 13 buying @1.0969723463058472\n", "@time 11 selling @1.098659634590149\n", "@time 13 buying @1.0990219116210938\n", "@time 11 selling @1.1018069982528687\n", "@time 13 buying @1.1101243495941162\n", "@time 11 selling @1.1176930665969849\n", "@time 13 buying @1.1196954250335693\n", "@time 11 selling @1.12271249294281\n", "@time 13 buying @1.1242270469665527\n", "@time 11 selling @1.123721718788147\n", "@time 13 buying @1.1224603652954102\n", "@time 11 selling @1.1256190538406372\n", "@time 13 buying @1.12296462059021\n", "@time 11 selling @1.1217050552368164\n", "@time 13 buying @1.1218308210372925\n", "@time 11 selling @1.1209505796432495\n", "@time 13 buying @1.1185681819915771\n", "@time 11 selling @1.1120996475219727\n", "@time 13 buying @1.1122233867645264\n", "@time 11 selling @1.1100010871887207\n", "@time 13 buying @1.1087703704833984\n", "@time 11 selling @1.1050944328308105\n", "@time 13 buying @1.1042402982711792\n", "@time 11 selling @1.1065618991851807\n", "@time 13 buying @1.1068068742752075\n", "@time 11 selling @1.1138337850570679\n", "@time 13 buying @1.1012003421783447\n", "@time 11 selling @1.1012003421783447\n", "@time 13 buying @1.1018069982528687\n", "@time 11 selling @1.1029006242752075\n", "@time 13 buying @1.1041183471679688\n", "@time 11 selling @1.0979359149932861\n", "@time 13 buying @1.0976948738098145\n", "@time 11 selling @1.0981770753860474\n", "@time 13 buying @1.0952903032302856\n", "@time 11 selling @1.0938525199890137\n", "@time 13 buying @1.0920606851577759\n", "@time 11 selling @1.0942115783691406\n", "@time 13 buying @1.1015641689300537\n", "@time 11 selling @1.0987803936004639\n", "@time 13 buying @1.0996261835098267\n", "@time 11 selling @1.0944511890411377\n", "@time 13 buying @1.0939722061157227\n", "@time 11 selling @1.0974539518356323\n", "@time 13 buying @1.0982975959777832\n", "@time 11 selling @1.1020498275756836\n", "@time 13 buying @1.1042402982711792\n", "@time 11 selling @1.0999890565872192\n", "@time 13 buying @1.096611499786377\n", "@time 11 selling @1.0943313837051392\n", "@time 13 buying @1.0883761644363403\n", "@time 11 selling @1.093135118484497\n", "@time 13 buying @1.0940918922424316\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.0913456678390503\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0901559591293335\n", "@time 11 selling @1.0866022109985352\n", "@time 13 buying @1.0874292850494385\n", "@time 11 selling @1.0911074876785278\n", "@time 13 buying @1.0890873670578003\n", "@time 11 selling @1.0888501405715942\n", "@time 13 buying @1.0847163200378418\n", "@time 11 selling @1.0814317464828491\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0847163200378418\n", "@time 11 selling @1.0807305574417114\n", "@time 13 buying @1.08119797706604\n", "@time 11 selling @1.0807305574417114\n", "@time 13 buying @1.080964207649231\n", "@time 11 selling @1.0807305574417114\n", "@time 13 buying @1.0807305574417114\n", "@time 11 selling @1.0882576704025269\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0853049755096436\n", "@time 11 selling @1.0837758779525757\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0803803205490112\n", "@time 13 buying @1.07991361618042\n", "@time 11 selling @1.0736525058746338\n", "@time 13 buying @1.0731916427612305\n", "@time 11 selling @1.0745755434036255\n", "@time 13 buying @1.0739984512329102\n", "@time 11 selling @1.0708931684494019\n", "@time 13 buying @1.06963312625885\n", "@time 11 selling @1.0702054500579834\n", "@time 13 buying @1.0727311372756958\n", "@time 11 selling @1.0721560716629028\n", "@time 13 buying @1.0738831758499146\n", "@time 11 selling @1.0721560716629028\n", "@time 13 buying @1.0722709894180298\n", "@time 11 selling @1.0739984512329102\n", "@time 13 buying @1.0744600296020508\n", "@time 11 selling @1.073306918144226\n", "@time 13 buying @1.0665528774261475\n", "@time 11 selling @1.0656436681747437\n", "@time 13 buying @1.0665528774261475\n", "@time 11 selling @1.0665528774261475\n", "@time 13 buying @1.066439151763916\n", "@time 11 selling @1.071237325668335\n", "@time 13 buying @1.069290041923523\n", "@time 11 selling @1.0705492496490479\n", "@time 13 buying @1.071926236152649\n", "@time 11 selling @1.0637166500091553\n", "@time 13 buying @1.0645092725753784\n", "@time 11 selling @1.0639430284500122\n", "@time 13 buying @1.0662117004394531\n", "@time 11 selling @1.06326425075531\n", "@time 13 buying @1.0604453086853027\n", "@time 11 selling @1.060333013534546\n", "@time 13 buying @1.0593219995498657\n", "@time 11 selling @1.0544073581695557\n", "@time 13 buying @1.0524100065231323\n", "@time 11 selling @1.0544073581695557\n", "@time 13 buying @1.0551862716674805\n", "@time 11 selling @1.0597710609436035\n", "@time 13 buying @1.0582010746002197\n", "@time 11 selling @1.0534077882766724\n", "@time 13 buying @1.0519671440124512\n", "@time 11 selling @1.0476689338684082\n", "@time 13 buying @1.048327922821045\n", "@time 11 selling @1.0500892400741577\n", "@time 13 buying @1.0510826110839844\n", "@time 11 selling @1.0526316165924072\n", "@time 13 buying @1.0527424812316895\n", "@time 11 selling @1.056859016418457\n", "@time 13 buying @1.051414132118225\n", "@time 11 selling @1.0530749559402466\n", "@time 13 buying @1.0555204153060913\n", "@time 11 selling @1.0578652620315552\n", "@time 13 buying @1.0597710609436035\n", "@time 11 selling @1.0606703758239746\n", "@time 13 buying @1.06326425075531\n", "@time 11 selling @1.062134861946106\n", "@time 13 buying @1.0554089546203613\n", "@time 11 selling @1.0527424812316895\n", "@time 13 buying @1.0529640913009644\n", "@time 11 selling @1.0539628267288208\n", "@time 13 buying @1.0531858205795288\n", "@time 11 selling @1.057753324508667\n", "@time 13 buying @1.0551862716674805\n", "@time 11 selling @1.0563008785247803\n", "@time 13 buying @1.055297613143921\n", "@time 11 selling @1.0560777187347412\n", "@time 13 buying @1.0578652620315552\n", "@time 11 selling @1.0594342947006226\n", "@time 13 buying @1.0583131313323975\n", "@time 11 selling @1.0605578422546387\n", "@time 13 buying @1.060782790184021\n", "@time 11 selling @1.063829779624939\n", "@time 13 buying @1.0613458156585693\n", "@time 11 selling @1.0575296878814697\n", "@time 13 buying @1.0583131313323975\n", "@time 11 selling @1.053851842880249\n", "@time 13 buying @1.0549635887145996\n", "@time 11 selling @1.0539628267288208\n", "@time 13 buying @1.0583131313323975\n", "@time 11 selling @1.0587612390518188\n", "@time 13 buying @1.0612331628799438\n", "@time 11 selling @1.0659844875335693\n", "@time 13 buying @1.0605578422546387\n", "@time 11 selling @1.0546298027038574\n", "@time 13 buying @1.0535187721252441\n", "@time 11 selling @1.0637166500091553\n", "@time 13 buying @1.0649627447128296\n", "@time 11 selling @1.0649627447128296\n", "@time 13 buying @1.0720411539077759\n", "@time 11 selling @1.075384497642517\n", "@time 13 buying @1.0741138458251953\n", "@time 11 selling @1.0676915645599365\n", "@time 13 buying @1.0678056478500366\n", "@time 11 selling @1.0679197311401367\n", "@time 13 buying @1.0672358274459839\n", "@time 11 selling @1.0691757202148438\n", "@time 13 buying @1.0711225271224976\n", "@time 11 selling @1.0680338144302368\n", "@time 13 buying @1.069290041923523\n", "@time 11 selling @1.0689470767974854\n", "@time 13 buying @1.067463755607605\n", "@time 11 selling @1.0721560716629028\n", "@time 13 buying @1.0793308019638062\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.0863661766052246\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.0882576704025269\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.0874292850494385\n", "@time 11 selling @1.0925379991531372\n", "@time 13 buying @1.0930156707763672\n", "@time 11 selling @1.0942115783691406\n", "@time 13 buying @1.0946906805038452\n", "@time 11 selling @1.0903936624526978\n", "@time 13 buying @1.0900371074676514\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.0905125141143799\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.0924185514450073\n", "@time 11 selling @1.095650315284729\n", "@time 13 buying @1.0948106050491333\n", "@time 11 selling @1.0952903032302856\n", "@time 13 buying @1.0975743532180786\n", "@time 11 selling @1.0987803936004639\n", "@time 13 buying @1.0975743532180786\n", "@time 11 selling @1.0918222665786743\n", "@time 13 buying @1.0918222665786743\n", "@time 11 selling @1.0900371074676514\n", "@time 13 buying @1.087074637413025\n", "@time 11 selling @1.0871928930282593\n", "@time 13 buying @1.0856584310531616\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0831888914108276\n", "@time 11 selling @1.078399658203125\n", "@time 13 buying @1.0800302028656006\n", "@time 11 selling @1.0773539543151855\n", "@time 13 buying @1.0793308019638062\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.0751532316207886\n", "@time 11 selling @1.0771219730377197\n", "@time 13 buying @1.076774001121521\n", "@time 11 selling @1.07991361618042\n", "@time 13 buying @1.077818512916565\n", "@time 11 selling @1.0787487030029297\n", "@time 13 buying @1.0803803205490112\n", "@time 11 selling @1.0932546854019165\n", "@time 13 buying @1.0946906805038452\n", "@time 11 selling @1.0970927476882935\n", "@time 13 buying @1.0920606851577759\n", "@time 11 selling @1.0915838479995728\n", "@time 13 buying @1.0925379991531372\n", "@time 11 selling @1.0939722061157227\n", "@time 13 buying @1.0964912176132202\n", "@time 11 selling @1.0940918922424316\n", "@time 13 buying @1.0963709354400635\n", "@time 11 selling @1.0982975959777832\n", "@time 13 buying @1.0995051860809326\n", "@time 11 selling @1.1027790307998657\n", "@time 13 buying @1.1031439304351807\n", "@time 11 selling @1.1016855239868164\n", "@time 13 buying @1.1021713018417358\n", "@time 11 selling @1.1061947345733643\n", "@time 13 buying @1.107419729232788\n", "@time 11 selling @1.1122233867645264\n", "@time 13 buying @1.1076650619506836\n", "@time 11 selling @1.1079105138778687\n", "@time 13 buying @1.1052166223526\n", "@time 11 selling @1.0972130298614502\n", "@time 13 buying @1.0957703590393066\n", "@time 11 selling @1.0922993421554565\n", "@time 13 buying @1.0924185514450073\n", "@time 11 selling @1.095170259475708\n", "@time 13 buying @1.09505033493042\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.091941475868225\n", "@time 11 selling @1.0940918922424316\n", "@time 13 buying @1.0954102277755737\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0945708751678467\n", "@time 11 selling @1.0948106050491333\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.0981770753860474\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.0954102277755737\n", "@time 13 buying @1.0958904027938843\n", "@time 11 selling @1.0949305295944214\n", "@time 13 buying @1.095170259475708\n", "@time 11 selling @1.0894432067871094\n", "@time 13 buying @1.0875475406646729\n", "@time 11 selling @1.0882576704025269\n", "@time 13 buying @1.0866022109985352\n", "@time 11 selling @1.0892059803009033\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0890873670578003\n", "@time 13 buying @1.088139295578003\n", "@time 11 selling @1.0893245935440063\n", "@time 13 buying @1.089680790901184\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.0877841711044312\n", "@time 11 selling @1.089561939239502\n", "@time 13 buying @1.0928961038589478\n", "@time 11 selling @1.0897995233535767\n", "@time 13 buying @1.088139295578003\n", "@time 11 selling @1.0876659154891968\n", "@time 13 buying @1.0874292850494385\n", "@time 11 selling @1.0824854373931885\n", "@time 13 buying @1.08236825466156\n", "@time 11 selling @1.084481120109558\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0842459201812744\n", "@time 13 buying @1.0862481594085693\n", "@time 11 selling @1.08119797706604\n", "@time 13 buying @1.0817828178405762\n", "@time 11 selling @1.0886130332946777\n", "@time 13 buying @1.08119797706604\n", "@time 11 selling @1.0757315158843994\n", "@time 13 buying @1.0739984512329102\n", "@time 11 selling @1.0742292404174805\n", "@time 13 buying @1.073306918144226\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0773539543151855\n", "@time 11 selling @1.0765421390533447\n", "@time 13 buying @1.074806571006775\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0786322355270386\n", "@time 11 selling @1.0773539543151855\n", "@time 13 buying @1.076658010482788\n", "@time 11 selling @1.0781670808792114\n", "@time 13 buying @1.0710078477859497\n", "@time 11 selling @1.0711225271224976\n", "@time 13 buying @1.0711225271224976\n", "@time 11 selling @1.0738831758499146\n", "@time 13 buying @1.0771219730377197\n", "@time 11 selling @1.0781670808792114\n", "@time 13 buying @1.0750375986099243\n", "@time 11 selling @1.0780508518218994\n", "@time 13 buying @1.0772379636764526\n", "@time 11 selling @1.0801469087600708\n", "@time 13 buying @1.082602620124817\n", "@time 11 selling @1.0801469087600708\n", "@time 13 buying @1.0813149213790894\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.082602620124817\n", "@time 11 selling @1.0829542875289917\n", "@time 13 buying @1.0835410356521606\n", "@time 11 selling @1.0854227542877197\n", "@time 13 buying @1.086130142211914\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0850694179534912\n", "@time 11 selling @1.0820168256759644\n", "@time 13 buying @1.0830715894699097\n", "@time 11 selling @1.0837758779525757\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.081548810005188\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0851871967315674\n", "@time 11 selling @1.0851871967315674\n", "@time 13 buying @1.0849517583847046\n", "@time 11 selling @1.087902545928955\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0875475406646729\n", "@time 11 selling @1.0936132669448853\n", "@time 13 buying @1.0963709354400635\n", "@time 11 selling @1.0945708751678467\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.0937328338623047\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.0945708751678467\n", "@time 13 buying @1.0939722061157227\n", "@time 11 selling @1.0943313837051392\n", "@time 13 buying @1.0915838479995728\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0889687538146973\n", "@time 11 selling @1.0906314849853516\n", "@time 13 buying @1.0892059803009033\n", "@time 11 selling @1.0853049755096436\n", "@time 13 buying @1.0856584310531616\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0848339796066284\n", "@time 11 selling @1.091226577758789\n", "@time 13 buying @1.0888501405715942\n", "@time 11 selling @1.0817828178405762\n", "@time 13 buying @1.0830715894699097\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0856584310531616\n", "@time 11 selling @1.0822510719299316\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.0817828178405762\n", "@time 11 selling @1.079097867012024\n", "@time 13 buying @1.080263614654541\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.077702283859253\n", "@time 11 selling @1.0756157636642456\n", "@time 13 buying @1.076774001121521\n", "@time 11 selling @1.0787487030029297\n", "@time 13 buying @1.079214334487915\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.087311029434204\n", "@time 11 selling @1.0838934183120728\n", "@time 13 buying @1.0803803205490112\n", "@time 11 selling @1.0827198028564453\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0864840745925903\n", "@time 13 buying @1.088020920753479\n", "@time 11 selling @1.085894227027893\n", "@time 13 buying @1.0757315158843994\n", "@time 11 selling @1.0731916427612305\n", "@time 13 buying @1.073767900466919\n", "@time 11 selling @1.065303087234497\n", "@time 13 buying @1.0645092725753784\n", "@time 11 selling @1.065757155418396\n", "@time 13 buying @1.0639430284500122\n", "@time 11 selling @1.0646226406097412\n", "@time 13 buying @1.063490390777588\n", "@time 11 selling @1.063829779624939\n", "@time 13 buying @1.064736008644104\n", "@time 11 selling @1.0680338144302368\n", "@time 13 buying @1.0654165744781494\n", "@time 11 selling @1.0654165744781494\n", "@time 13 buying @1.066894292831421\n", "@time 11 selling @1.0637166500091553\n", "@time 13 buying @1.0636035203933716\n", "@time 11 selling @1.0676915645599365\n", "@time 13 buying @1.0704345703125\n", "@time 11 selling @1.0691757202148438\n", "@time 13 buying @1.06963312625885\n", "@time 11 selling @1.0721560716629028\n", "@time 13 buying @1.0706638097763062\n", "@time 11 selling @1.071926236152649\n", "@time 13 buying @1.0710078477859497\n", "@time 11 selling @1.071811318397522\n", "@time 13 buying @1.0702054500579834\n", "@time 11 selling @1.0722709894180298\n", "@time 13 buying @1.0705492496490479\n", "@time 11 selling @1.067463755607605\n", "@time 13 buying @1.0686043500900269\n", "@time 11 selling @1.0705492496490479\n", "@time 13 buying @1.0690613985061646\n", "@time 11 selling @1.0749220848083496\n", "@time 13 buying @1.0781670808792114\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0789813995361328\n", "@time 11 selling @1.0770059823989868\n", "@time 13 buying @1.078399658203125\n", "@time 11 selling @1.0749220848083496\n", "@time 13 buying @1.0752688646316528\n", "@time 11 selling @1.0730764865875244\n", "@time 13 buying @1.076774001121521\n", "@time 11 selling @1.078283429145813\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.079214334487915\n", "@time 13 buying @1.0807305574417114\n", "@time 11 selling @1.079563856124878\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.085894227027893\n", "@time 11 selling @1.0847163200378418\n", "@time 13 buying @1.085894227027893\n", "@time 11 selling @1.0862481594085693\n", "@time 13 buying @1.08695650100708\n", "@time 11 selling @1.0871928930282593\n", "@time 13 buying @1.0853049755096436\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.0837758779525757\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0845986604690552\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0857763290405273\n", "@time 11 selling @1.0887316465377808\n", "@time 13 buying @1.0868383646011353\n", "@time 11 selling @1.0853049755096436\n", "@time 13 buying @1.0841283798217773\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0833063125610352\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.087074637413025\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0864840745925903\n", "@time 13 buying @1.08695650100708\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0888501405715942\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.0901559591293335\n", "@time 13 buying @1.0822510719299316\n", "@time 11 selling @1.0757315158843994\n", "@time 13 buying @1.0743446350097656\n", "@time 11 selling @1.0730764865875244\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.076774001121521\n", "@time 13 buying @1.0840108394622803\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.0697474479675293\n", "@time 13 buying @1.0672358274459839\n", "@time 11 selling @1.071237325668335\n", "@time 13 buying @1.071811318397522\n", "@time 11 selling @1.0720411539077759\n", "@time 13 buying @1.0749220848083496\n", "@time 11 selling @1.0752688646316528\n", "@time 13 buying @1.0746910572052002\n", "@time 11 selling @1.071811318397522\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.069290041923523\n", "@time 13 buying @1.0687185525894165\n", "@time 11 selling @1.073767900466919\n", "@time 13 buying @1.0745755434036255\n", "@time 11 selling @1.071237325668335\n", "@time 13 buying @1.0704345703125\n", "@time 11 selling @1.0697474479675293\n", "@time 13 buying @1.0678056478500366\n", "@time 11 selling @1.06963312625885\n", "@time 13 buying @1.0725010633468628\n", "@time 11 selling @1.0699764490127563\n", "@time 13 buying @1.0702054500579834\n", "@time 11 selling @1.0752688646316528\n", "@time 13 buying @1.0757315158843994\n", "@time 11 selling @1.0726161003112793\n", "@time 13 buying @1.0745755434036255\n", "@time 11 selling @1.0760787725448608\n", "@time 13 buying @1.0793308019638062\n", "@time 11 selling @1.0800302028656006\n", "@time 13 buying @1.0804970264434814\n", "@time 11 selling @1.0822510719299316\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0836584568023682\n", "@time 13 buying @1.0843634605407715\n", "@time 11 selling @1.0816657543182373\n", "@time 13 buying @1.0824854373931885\n", "@time 11 selling @1.0829542875289917\n", "@time 13 buying @1.0827198028564453\n", "@time 11 selling @1.085894227027893\n", "@time 13 buying @1.0890873670578003\n", "@time 11 selling @1.0889687538146973\n", "@time 13 buying @1.0897995233535767\n", "@time 11 selling @1.090869426727295\n", "@time 13 buying @1.0918222665786743\n", "@time 11 selling @1.0905125141143799\n", "@time 13 buying @1.0886130332946777\n", "@time 11 selling @1.0938525199890137\n", "@time 13 buying @1.0940918922424316\n", "@time 11 selling @1.0930156707763672\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.089561939239502\n", "@time 13 buying @1.0884946584701538\n", "@time 11 selling @1.0887316465377808\n", "@time 13 buying @1.0883761644363403\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0851871967315674\n", "@time 11 selling @1.0849517583847046\n", "@time 13 buying @1.0857763290405273\n", "@time 11 selling @1.0848339796066284\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.0863661766052246\n", "@time 13 buying @1.0868383646011353\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.0828369855880737\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.0831888914108276\n", "@time 13 buying @1.0845986604690552\n", "@time 11 selling @1.0794472694396973\n", "@time 13 buying @1.0807305574417114\n", "@time 11 selling @1.0834236145019531\n", "@time 13 buying @1.090869426727295\n", "@time 11 selling @1.096611499786377\n", "@time 13 buying @1.0981770753860474\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.0926573276519775\n", "@time 13 buying @1.0920606851577759\n", "@time 11 selling @1.0933741331100464\n", "@time 13 buying @1.0894432067871094\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0925379991531372\n", "@time 11 selling @1.0928961038589478\n", "@time 13 buying @1.0928961038589478\n", "@time 11 selling @1.0933741331100464\n", "@time 13 buying @1.0962508916854858\n", "@time 11 selling @1.1025358438491821\n", "@time 13 buying @1.1041183471679688\n", "@time 11 selling @1.1015641689300537\n", "@time 13 buying @1.0969723463058472\n", "@time 11 selling @1.0995051860809326\n", "@time 13 buying @1.100473165512085\n"]}, {"data": {"text/plain": ["[<__main__.<PERSON><PERSON><PERSON><PERSON> at 0x204d5b7e240>]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro.run()"]}, {"cell_type": "code", "execution_count": 18, "id": "af62ce74-18f8-4afa-bccb-ed5ea41afde8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x550 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[<Figure size 2000x1375 with 6 Axes>]]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import matplotlib.pyplot as plt\n", "plt.show()\n", "\n", "%matplotlib inline\n", "cerebro.plot(iplot=False, volume=False)"]}, {"cell_type": "code", "execution_count": null, "id": "38deaa99-3d32-480c-b9c5-205d5212dab8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3108c0b7-d262-480e-9bc3-41526065160e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}