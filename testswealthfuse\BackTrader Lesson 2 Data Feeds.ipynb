import backtrader as bt
import pandas as pd
import yfinance as yf

tsla_daily = yf.download(tickers='TSLA', start='2025-06-27')
tsla_hourly = yf.download(tickers='TSLA', start='2025-06-27', interval='1h')

tsla_daily.info()

tsla_hourly.info()

tsla_daily_parsed = bt.feeds.PandasData(dataname=tsla_daily, datetime=None, open=0, high=1, low=2, close=4, volume=5, openinterest=None)

tsla_hourly_parsed = bt.feeds.PandasData(dataname=tsla_hourly, datetime=None, open=0, high=1, low=2, close=4, volume=5, openinterest=None, timeframe=bt.TimeFrame.Minutes)

cerebro = bt.Cerebro()
cerebro.adddata(tsla_daily_parsed)

tsla_daily.columns

cerebro.run()
%matplotlib inline
cerebro.plot(iplot=False)

%matplotlib inline
import matplotlib.pyplot as plt

plt.show()

cerebro = bt.Cerebro()

cerebro.adddata(tsla_daily_parsed)
cerebro.adddata(tsla_hourly_parsed)

cerebro.run()
%matplotlib inline
cerebro.plot(iplot=False)

cerebro = bt.Cerebro()

cerebro.resampledata(dataname=tsla_hourly_parsed, timeframe=bt.TimeFrame.Days, compression=5)
cerebro.adddata(tsla_daily_parsed)

cerebro.run()
%matplotlib inline
cerebro.plot(iplot=False)





